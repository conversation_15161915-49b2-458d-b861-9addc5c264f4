%tr = ( 
%tr,

'guardian' => 'Guardian',
'guardian block a host' => 'Host blocken',
'guardian block httpd brute-force' => 'httpd-Brute-Force-Erkennung',
'guardian block owncloud brute-force' => 'Owncloud-Brute-Force-Erkennung',
'guardian block ssh brute-force' => 'SSH-Brute-Force-Erkennung',
'guardian blockcount' => 'Trefferschwelle',
'guardian blocked hosts' => 'Aktuell geblockte Hosts',
'guardian blocking of this address is not allowed' => 'Diese Addresse darf nicht geblockt werden.',
'guardian blocktime' => 'Blockzeit (Sekunden)',
'guardian common settings' => 'Allgemeine Einstellungen',
'guardian configuration' => 'Guardian-Konfiguration',
'guardian daemon' => 'Daemon',
'guardian empty input' => 'Fehlende Angabe: Bitte geben Sie einen gültigen Host oder ein gültiges Netzwerk an.',
'guardian enabled' => 'Guardian aktivieren',
'guardian firewallaction' => 'Firewall-Aktion',
'guardian ignored hosts' => 'Ignorierte Hosts',
'guardian invalid address or subnet' => 'Ungültige Host-Addresse oder Netzwerk.',
'guardian invalid blockcount' => 'Ungültige Anzahl: Bitte verwenden Sie eine natürliche Zahl größer als Null.',
'guardian invalid blocktime' => 'Ungültige Blockzeit: Bitte verwenden Sie eine natürliche Zahl größer als Null.',
'guardian invalid logfile' => 'Der angegebene Pfad zum "Ignore file" ist ungültig.',
'guardian logfacility' => 'Logziel',
'guardian logfile' => 'Logdatei',
'guardian loglevel' => 'Loglevel',
'guardian loglevel_off' => 'Aus',
'guardian loglevel_info' => 'Info',
'guardian loglevel_debug' => 'Debug',
'guardian logtarget_syslog' => 'Systemlog',
'guardian logtarget_file' => 'Datei',
'guardian logtarget_console' => 'Konsole',
'guardian no entries' => 'Keine Einträge vorhanden.',
'guardian not running no hosts can be blocked' => 'Guardian läuft nicht. Es werden keine Hosts geblockt.',
'guardian priolevel_high' => '1 - Hoch',
'guardian priolevel_medium' => '2 - Mittel',
'guardian priolevel_low' => '3 - Niedrig',
'guardian priolevel_very_low' => '4 - Sehr niedrig',
'guardian service' => 'Guardian-Dienst',

);

#EOF

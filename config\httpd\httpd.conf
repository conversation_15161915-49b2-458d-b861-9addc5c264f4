# Apache2 server configuration file for IPFire

# run under this user/group id
Include /etc/httpd/conf/uid.conf

# - how many server processes to start (server pool regulation)
# - usage of KeepAlive
Include /etc/httpd/conf/server-tuning.conf

# ErrorLog: The location of the error log file
ErrorLog /var/log/httpd/error_log

# Load Modules here
Include /etc/httpd/conf/loadmodule.conf

# IP addresses and ports to listen on
Include /etc/httpd/conf/listen.conf

# predefined logging formats
Include /etc/httpd/conf/mod_log_config.conf

# global settings
Include /etc/httpd/conf/global.conf

# associate MIME types with filename extensions
TypesConfig /etc/mime.types

# global (server-wide) SSL configuration, that is not specific to any virtual host
Include /etc/httpd/conf/ssl-global.conf

<Directory />
    Options None
    AllowOverride None
</Directory>

# use .htaccess files for overriding,
AccessFileName .htaccess
# and never show them
<Files ~ "^\.ht">
    Require all denied
</Files>

# List of resources to look for when the client requests a directory
DirectoryIndex index.html index.htm index.shtml index.cgi

# 'Main' server configuration
Include /etc/httpd/conf/default-server.conf

# Virtual server configuration
Include /etc/httpd/conf/vhosts.d/*.conf

# EOF

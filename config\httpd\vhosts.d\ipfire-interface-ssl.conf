<VirtualHost *:444>

    RewriteEngine on
    RewriteCond %{REQUEST_METHOD} ^(TRACE|TRACK|OPTIONS)
    RewriteRule .* - [F]

    DocumentRoot /srv/web/ipfire/html
    ServerAdmin root@localhost
    ErrorLog /var/log/httpd/error_log
    TransferLog /var/log/httpd/access_log

    SSLEngine on
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite AESGCM+EECDH:CHACHA20+EECDH:@STRENGTH:+aRSA
    SSLHonorCipherOrder on
    SSLCompression off
    SSLSessionTickets off
    SSLCertificateFile /etc/httpd/server.crt
    SSLCertificateKeyFile /etc/httpd/server.key
    SSLCertificateFile /etc/httpd/server-ecdsa.crt
    SSLCertificateKeyFile /etc/httpd/server-ecdsa.key

    Header always set X-Content-Type-Options nosniff
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:"
    Header always set Referrer-Policy strict-origin
    Header always set X-Frame-Options sameorigin

    <Directory /srv/web/ipfire/html>
        Options ExecCGI
        AllowOverride None
        Require all granted
    </Directory>
    <DirectoryMatch "/srv/web/ipfire/html/(graphs|sgraph)">
        AuthName "IPFire - Restricted"
        AuthType Basic
        AuthUserFile /var/ipfire/auth/users
        <RequireAll>
            Require user admin
            Require ssl
        </RequireAll>
    </DirectoryMatch>
    ScriptAlias /cgi-bin/ /srv/web/ipfire/cgi-bin/
    <Directory /srv/web/ipfire/cgi-bin>
        AllowOverride None
        Options ExecCGI
        AuthName "IPFire - Restricted"
        AuthType Basic
        AuthUserFile /var/ipfire/auth/users
        <RequireAll>
            Require user admin
            Require ssl
        </RequireAll>
        <Files chpasswd.cgi>
            Require all granted
        </Files>
        <Files webaccess.cgi>
            Require all granted
        </Files>
    </Directory>
    <Files ~ "\.(cgi|shtml?)$">
	SSLOptions +StdEnvVars
    </Files>
    <Directory /srv/web/ipfire/cgi-bin>
	SSLOptions +StdEnvVars
    </Directory>
    SetEnv HOME /home/<USER>
    CustomLog /var/log/httpd/ssl_request_log \
	"%t %h %{SSL_PROTOCOL}x %{SSL_CIPHER}x \"%r\" %b"

    Alias /updatecache/ /var/updatecache/
	<Directory /var/updatecache>
		 Options ExecCGI
		 AllowOverride None
		 Require all granted
	</Directory>

    Alias /repository/ /var/urlrepo/
	<Directory /var/urlrepo>
		 Options ExecCGI
		 AllowOverride None
		 Require all granted
	</Directory>

    Alias /proxy-reports/ /var/log/sarg/
    <Directory /var/log/sarg>
        AllowOverride None
        Options None
        AuthName "IPFire - Restricted"
        AuthType Basic
        AuthUserFile /var/ipfire/auth/users
        <RequireAll>
            Require user admin
            Require ssl
        </RequireAll>
    </Directory>
</VirtualHost>

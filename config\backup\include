etc/conntrackd/conntrackd.conf
etc/group
etc/hosts*
etc/httpd/server.crt
etc/httpd/server.csr
etc/httpd/server-ecdsa.crt
etc/httpd/server-ecdsa.csr
etc/httpd/server-ecdsa.key
etc/httpd/server.key
etc/ipsec.user.*
etc/ipsec.user-post.conf
etc/logrotate.d
etc/passwd
etc/shadow
etc/ssh/sshd_config
etc/ssh/ssh_host*
etc/squid/squid.conf.local
etc/squid/squid.conf.pre.local
etc/sysconfig/*
etc/sysconfig/firewall.local
etc/sysconfig/rc.local
etc/unbound
root/.bash_history
root/.gitconfig
root/.ssh
srv/web/ipfire/html/proxy.pac
var/cache/suricata
var/ipfire/auth/users
var/ipfire/backup/addons/backup
var/ipfire/backup/exclude.user
var/ipfire/backup/include.user
var/ipfire/captive/*
var/ipfire/*/*.conf
var/ipfire/*/config
var/ipfire/dhcp/*
var/ipfire/dns
var/ipfire/dnsforward/*
var/ipfire/*/enable
var/ipfire/*/*enable*
var/ipfire/ethernet/aliases
var/ipfire/ethernet/wireless
var/ipfire/firewall
var/ipfire/fwhosts
var/ipfire/ipblocklist/modified
var/ipfire/main/*
var/ipfire/ovpn
var/ipfire/ovpn/collectd.vpn
var/ipfire/*/*.pem
var/ipfire/ppp
var/ipfire/proxy
var/ipfire/qos/*
var/ipfire/qos/bin/qos.sh
var/ipfire/suricata
var/ipfire/*/settings
var/ipfire/time/
var/ipfire/urlfilter
var/ipfire/vpn
var/lib/ipblocklist
var/lib/suricata
var/log/ip-acct/*
var/log/rrd/*
var/log/rrd/collectd
var/log/vnstat

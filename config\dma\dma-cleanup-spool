#!/bin/bash
###############################################################################
#                                                                             #
# IPFire.org - A linux based firewall                                         #
# Copyright (C) 2015 <PERSON> <<EMAIL>>               #
#                                                                             #
# This program is free software: you can redistribute it and/or modify        #
# it under the terms of the GNU General Public License as published by        #
# the Free Software Foundation, either version 3 of the License, or           #
# (at your option) any later version.                                         #
#                                                                             #
# This program is distributed in the hope that it will be useful,             #
# but WITHOUT ANY WARRANTY; without even the implied warranty of              #
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the               #
# GNU General Public License for more details.                                #
#                                                                             #
# You should have received a copy of the GNU General Public License           #
# along with this program.  If not, see <http://www.gnu.org/licenses/>.       #
#                                                                             #
###############################################################################

SPOOL_DIR="/var/spool/dma"

find_messages() {
	find "${SPOOL_DIR}" -type f -name "M*" -mtime +30
}

remove_message() {
	local f_message="${1}"
	local f_queue="${f_message/${SPOOL_DIR}\/M/${SPOOL_DIR}\/Q}"

	# If a message file and a queue file exist, delete both
	[ -f "${f_message}" ] || return 1
	[ -f "${f_queue}" ] || return 1

	rm -f "${f_message}" "${f_queue}"
	return 0
}

main() {
	for message in $(find_messages); do
		remove_message "${message}"
	done

	return 0
}

main || exit $?

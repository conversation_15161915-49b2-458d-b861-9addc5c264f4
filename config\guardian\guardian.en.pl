%tr = ( 
%tr,

'guardian' => 'Guardian',
'guardian block a host' => 'Block host',
'guardian block httpd brute-force' => 'httpd Brute Force Detection',
'guardian block owncloud brute-force' => 'Owncloud Brute Force detection',
'guardian block ssh brute-force' => 'SSH Brute Force Detection',
'guardian blockcount' => 'Strike Threshold',
'guardian blocked hosts' => 'Currently blocked hosts',
'guardian blocking of this address is not allowed' => 'Blocking of the given address is not allowed.',
'guardian blocktime' => 'Block Time (seconds)',
'guardian common settings' => 'Common Settings',
'guardian configuration' => 'Guardian Configuration',
'guardian daemon' => 'Daemon',
'guardian empty input' => 'Empty input: Please enter a valid host address or subnet.',
'guardian enabled' => 'Enable Guardian',
'guardian firewallaction' => 'Firewall Action',
'guardian ignored hosts' => 'Ignored Hosts',
'guardian invalid address or subnet' => 'Invalid host address or subnet.',
'guardian invalid blockcount' => 'Invalid BlockCount: Please provide a natural number higher than zero.',
'guardian invalid blocktime' => 'Invalid BlockTime: Please provide a natural number higher than zero.',
'guardian invalid logfile' => 'The provided path for the logfile is not valid.',
'guardian logfacility' => 'Log Facility',
'guardian logfile' => 'Log File',
'guardian loglevel' => 'Log Level',
'guardian loglevel_off' => 'Off',
'guardian loglevel_info' => 'Info',
'guardian loglevel_debug' => 'Debug',
'guardian logtarget_syslog' => 'Systemlog',
'guardian logtarget_file' => 'File',
'guardian logtarget_console' => 'Console',
'guardian no entries' => 'No entries at the moment.',
'guardian not running no hosts can be blocked' => 'Guardian is not running. No hosts will be blocked.',
'guardian priolevel_high' => '1 - High',
'guardian priolevel_medium' => '2 - Medium',
'guardian priolevel_low' => '3 - Low',
'guardian priolevel_very_low' => '4 - Very low',
'guardian service' => 'Guardian Service',

);

#EOF

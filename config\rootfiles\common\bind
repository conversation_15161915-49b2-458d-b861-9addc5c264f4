etc/trusted-key.key
usr/bin/dig
usr/bin/host
usr/bin/nslookup
usr/bin/nsupdate
#usr/include/bind9
#usr/include/bind9/check.h
#usr/include/bind9/getaddresses.h
#usr/include/bind9/version.h
#usr/include/dns
#usr/include/dns/acl.h
#usr/include/dns/adb.h
#usr/include/dns/badcache.h
#usr/include/dns/bit.h
#usr/include/dns/byaddr.h
#usr/include/dns/cache.h
#usr/include/dns/callbacks.h
#usr/include/dns/catz.h
#usr/include/dns/cert.h
#usr/include/dns/client.h
#usr/include/dns/clientinfo.h
#usr/include/dns/compress.h
#usr/include/dns/db.h
#usr/include/dns/dbiterator.h
#usr/include/dns/dbtable.h
#usr/include/dns/diff.h
#usr/include/dns/dispatch.h
#usr/include/dns/dlz.h
#usr/include/dns/dlz_dlopen.h
#usr/include/dns/dns64.h
#usr/include/dns/dnsrps.h
#usr/include/dns/dnssec.h
#usr/include/dns/dnstap.h
#usr/include/dns/ds.h
#usr/include/dns/dsdigest.h
#usr/include/dns/dyndb.h
#usr/include/dns/ecdb.h
#usr/include/dns/ecs.h
#usr/include/dns/edns.h
#usr/include/dns/enumclass.h
#usr/include/dns/enumtype.h
#usr/include/dns/events.h
#usr/include/dns/fixedname.h
#usr/include/dns/forward.h
#usr/include/dns/geoip.h
#usr/include/dns/ipkeylist.h
#usr/include/dns/iptable.h
#usr/include/dns/journal.h
#usr/include/dns/kasp.h
#usr/include/dns/keydata.h
#usr/include/dns/keyflags.h
#usr/include/dns/keymgr.h
#usr/include/dns/keytable.h
#usr/include/dns/keyvalues.h
#usr/include/dns/lib.h
#usr/include/dns/librpz.h
#usr/include/dns/lmdb.h
#usr/include/dns/log.h
#usr/include/dns/lookup.h
#usr/include/dns/master.h
#usr/include/dns/masterdump.h
#usr/include/dns/message.h
#usr/include/dns/name.h
#usr/include/dns/ncache.h
#usr/include/dns/nsec.h
#usr/include/dns/nsec3.h
#usr/include/dns/nta.h
#usr/include/dns/opcode.h
#usr/include/dns/order.h
#usr/include/dns/peer.h
#usr/include/dns/portlist.h
#usr/include/dns/private.h
#usr/include/dns/rbt.h
#usr/include/dns/rcode.h
#usr/include/dns/rdata.h
#usr/include/dns/rdataclass.h
#usr/include/dns/rdatalist.h
#usr/include/dns/rdataset.h
#usr/include/dns/rdatasetiter.h
#usr/include/dns/rdataslab.h
#usr/include/dns/rdatastruct.h
#usr/include/dns/rdatatype.h
#usr/include/dns/request.h
#usr/include/dns/resolver.h
#usr/include/dns/result.h
#usr/include/dns/rootns.h
#usr/include/dns/rpz.h
#usr/include/dns/rriterator.h
#usr/include/dns/rrl.h
#usr/include/dns/sdb.h
#usr/include/dns/sdlz.h
#usr/include/dns/secalg.h
#usr/include/dns/secproto.h
#usr/include/dns/soa.h
#usr/include/dns/ssu.h
#usr/include/dns/stats.h
#usr/include/dns/tcpmsg.h
#usr/include/dns/time.h
#usr/include/dns/timer.h
#usr/include/dns/tkey.h
#usr/include/dns/tsec.h
#usr/include/dns/tsig.h
#usr/include/dns/ttl.h
#usr/include/dns/types.h
#usr/include/dns/update.h
#usr/include/dns/validator.h
#usr/include/dns/version.h
#usr/include/dns/view.h
#usr/include/dns/xfrin.h
#usr/include/dns/zone.h
#usr/include/dns/zonekey.h
#usr/include/dns/zoneverify.h
#usr/include/dns/zt.h
#usr/include/dst
#usr/include/dst/dst.h
#usr/include/dst/gssapi.h
#usr/include/dst/result.h
#usr/include/irs
#usr/include/irs/context.h
#usr/include/irs/dnsconf.h
#usr/include/irs/netdb.h
#usr/include/irs/platform.h
#usr/include/irs/resconf.h
#usr/include/irs/types.h
#usr/include/irs/version.h
#usr/include/isc
#usr/include/isc/aes.h
#usr/include/isc/align.h
#usr/include/isc/app.h
#usr/include/isc/assertions.h
#usr/include/isc/astack.h
#usr/include/isc/atomic.h
#usr/include/isc/backtrace.h
#usr/include/isc/barrier.h
#usr/include/isc/base32.h
#usr/include/isc/base64.h
#usr/include/isc/bind9.h
#usr/include/isc/buffer.h
#usr/include/isc/bufferlist.h
#usr/include/isc/cmocka.h
#usr/include/isc/commandline.h
#usr/include/isc/condition.h
#usr/include/isc/counter.h
#usr/include/isc/crc64.h
#usr/include/isc/deprecated.h
#usr/include/isc/dir.h
#usr/include/isc/endian.h
#usr/include/isc/errno.h
#usr/include/isc/error.h
#usr/include/isc/event.h
#usr/include/isc/eventclass.h
#usr/include/isc/file.h
#usr/include/isc/formatcheck.h
#usr/include/isc/fsaccess.h
#usr/include/isc/fuzz.h
#usr/include/isc/hash.h
#usr/include/isc/heap.h
#usr/include/isc/hex.h
#usr/include/isc/hmac.h
#usr/include/isc/ht.h
#usr/include/isc/httpd.h
#usr/include/isc/interfaceiter.h
#usr/include/isc/iterated_hash.h
#usr/include/isc/lang.h
#usr/include/isc/lex.h
#usr/include/isc/lfsr.h
#usr/include/isc/lib.h
#usr/include/isc/likely.h
#usr/include/isc/list.h
#usr/include/isc/log.h
#usr/include/isc/magic.h
#usr/include/isc/managers.h
#usr/include/isc/md.h
#usr/include/isc/mem.h
#usr/include/isc/meminfo.h
#usr/include/isc/mutex.h
#usr/include/isc/mutexblock.h
#usr/include/isc/net.h
#usr/include/isc/netaddr.h
#usr/include/isc/netdb.h
#usr/include/isc/netmgr.h
#usr/include/isc/netscope.h
#usr/include/isc/nonce.h
#usr/include/isc/offset.h
#usr/include/isc/once.h
#usr/include/isc/os.h
#usr/include/isc/parseint.h
#usr/include/isc/platform.h
#usr/include/isc/pool.h
#usr/include/isc/portset.h
#usr/include/isc/print.h
#usr/include/isc/quota.h
#usr/include/isc/radix.h
#usr/include/isc/random.h
#usr/include/isc/ratelimiter.h
#usr/include/isc/refcount.h
#usr/include/isc/regex.h
#usr/include/isc/region.h
#usr/include/isc/resource.h
#usr/include/isc/result.h
#usr/include/isc/resultclass.h
#usr/include/isc/rwlock.h
#usr/include/isc/safe.h
#usr/include/isc/serial.h
#usr/include/isc/siphash.h
#usr/include/isc/sockaddr.h
#usr/include/isc/socket.h
#usr/include/isc/stat.h
#usr/include/isc/stats.h
#usr/include/isc/stdatomic.h
#usr/include/isc/stdio.h
#usr/include/isc/stdtime.h
#usr/include/isc/strerr.h
#usr/include/isc/string.h
#usr/include/isc/symtab.h
#usr/include/isc/syslog.h
#usr/include/isc/task.h
#usr/include/isc/taskpool.h
#usr/include/isc/thread.h
#usr/include/isc/time.h
#usr/include/isc/timer.h
#usr/include/isc/tm.h
#usr/include/isc/types.h
#usr/include/isc/url.h
#usr/include/isc/utf8.h
#usr/include/isc/util.h
#usr/include/isc/version.h
#usr/include/isccc
#usr/include/isccc/alist.h
#usr/include/isccc/base64.h
#usr/include/isccc/cc.h
#usr/include/isccc/ccmsg.h
#usr/include/isccc/events.h
#usr/include/isccc/result.h
#usr/include/isccc/sexpr.h
#usr/include/isccc/symtab.h
#usr/include/isccc/symtype.h
#usr/include/isccc/types.h
#usr/include/isccc/util.h
#usr/include/isccc/version.h
#usr/include/isccfg
#usr/include/isccfg/aclconf.h
#usr/include/isccfg/cfg.h
#usr/include/isccfg/dnsconf.h
#usr/include/isccfg/grammar.h
#usr/include/isccfg/kaspconf.h
#usr/include/isccfg/log.h
#usr/include/isccfg/namedconf.h
#usr/include/isccfg/version.h
#usr/include/ns
#usr/include/ns/client.h
#usr/include/ns/hooks.h
#usr/include/ns/interfacemgr.h
#usr/include/ns/lib.h
#usr/include/ns/listenlist.h
#usr/include/ns/log.h
#usr/include/ns/notify.h
#usr/include/ns/query.h
#usr/include/ns/server.h
#usr/include/ns/sortlist.h
#usr/include/ns/stats.h
#usr/include/ns/types.h
#usr/include/ns/update.h
#usr/include/ns/version.h
#usr/include/ns/xfrout.h
#usr/include/pk11
#usr/include/pk11/constants.h
#usr/include/pk11/internal.h
#usr/include/pk11/pk11.h
#usr/include/pk11/result.h
#usr/include/pk11/site.h
#usr/include/pkcs11
#usr/include/pkcs11/pkcs11.h
usr/lib/libbind9-9.16.48.so
#usr/lib/libbind9.la
#usr/lib/libbind9.so
usr/lib/libdns-9.16.48.so
#usr/lib/libdns.la
#usr/lib/libdns.so
usr/lib/libirs-9.16.48.so
#usr/lib/libirs.la
#usr/lib/libirs.so
usr/lib/libisc-9.16.48.so
#usr/lib/libisc.la
#usr/lib/libisc.so
usr/lib/libisccc-9.16.48.so
#usr/lib/libisccc.la
#usr/lib/libisccc.so
usr/lib/libisccfg-9.16.48.so
#usr/lib/libisccfg.la
#usr/lib/libisccfg.so
usr/lib/libns-9.16.48.so
#usr/lib/libns.la
#usr/lib/libns.so

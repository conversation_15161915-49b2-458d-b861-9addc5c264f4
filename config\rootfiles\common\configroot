usr/sbin/convert-dmz
usr/sbin/convert-outgoingfw
usr/sbin/convert-portfw
usr/sbin/convert-snort
usr/sbin/convert-xtaccess
usr/sbin/convert-ids-modifysids-file
usr/sbin/convert-ids-multiple-providers
usr/sbin/firewall-policy
#var/ipfire
var/ipfire/addon-lang
var/ipfire/auth
#var/ipfire/auth/users
#var/ipfire/backup
var/ipfire/backup/exclude.user
var/ipfire/backup/include.user
var/ipfire/ca
var/ipfire/captive
var/ipfire/captive/agb.txt
var/ipfire/captive/clients
var/ipfire/captive/settings
var/ipfire/captive/voucher_out
var/ipfire/certs
#var/ipfire/certs/index.txt
var/ipfire/certs/index.txt.attr
#var/ipfire/certs/serial
var/ipfire/connscheduler
#var/ipfire/connscheduler/connscheduler.conf
#var/ipfire/connscheduler/lib.pl
var/ipfire/countries.pl
var/ipfire/crls
var/ipfire/ddns
#var/ipfire/ddns/config
#var/ipfire/ddns/ipcache
#var/ipfire/ddns/settings
var/ipfire/dhcp
#var/ipfire/dhcp/advoptions
#var/ipfire/dhcp/advoptions-list
#var/ipfire/dhcp/dhcpd.conf.local
#var/ipfire/dhcp/fixleases
#var/ipfire/dhcp/settings
var/ipfire/dhcpc
var/ipfire/dns
#var/ipfire/dns/settings
#var/ipfire/dns/servers
var/ipfire/dnsforward
#var/ipfire/dnsforward/config
var/ipfire/ethernet
#var/ipfire/ethernet/aliases
#var/ipfire/ethernet/known_nics
#var/ipfire/ethernet/scanned_nics
#var/ipfire/ethernet/settings
#var/ipfire/ethernet/vlans
#var/ipfire/ethernet/wireless
var/ipfire/extrahd
var/ipfire/extrahd/bin
var/ipfire/extrahd/bin/extrahd.pl
#var/ipfire/extrahd/devices
#var/ipfire/extrahd/partitions
#var/ipfire/extrahd/scan
#var/ipfire/extrahd/settings
var/ipfire/firewall
#var/ipfire/firewall/config
#var/ipfire/firewall/input
#var/ipfire/firewall/locationblock
#var/ipfire/firewall/outgoing
#var/ipfire/firewall/settings
var/ipfire/fwhosts
#var/ipfire/fwhosts/customgroups
#var/ipfire/fwhosts/customhosts
#var/ipfire/fwhosts/customlocationgrp
#var/ipfire/fwhosts/customnetworks
#var/ipfire/fwhosts/customservicegrp
#var/ipfire/fwhosts/customservices
#var/ipfire/fwhosts/customservices.default
#var/ipfire/fwhosts/icmp-types
var/ipfire/fwlogs
#var/ipfire/fwlogs/ipsettings
#var/ipfire/fwlogs/portsettings
var/ipfire/general-functions.pl
var/ipfire/graphs.pl
var/ipfire/header.pl
var/ipfire/location-functions.pl
var/ipfire/ids-functions.pl
var/ipfire/ipblocklist-functions.pl
var/ipfire/ipblocklist
#var/ipfire/ipblocklist/modified
#var/ipfire/ipblocklist/settings
var/ipfire/key
var/ipfire/lang.pl
var/ipfire/langs
#var/ipfire/langs/de.pl
#var/ipfire/langs/en.pl
#var/ipfire/langs/es.pl
#var/ipfire/langs/fr.pl
#var/ipfire/langs/it.pl
#var/ipfire/langs/list
#var/ipfire/langs/nl.pl
#var/ipfire/langs/pl.pl
#var/ipfire/langs/ru.pl
#var/ipfire/langs/tr.pl
#var/ipfire/langs/zh.pl
var/ipfire/logging
#var/ipfire/logging/settings
var/ipfire/mac
#var/ipfire/mac/settings
var/ipfire/main
#var/ipfire/main/hosts
#var/ipfire/main/routing
#var/ipfire/main/security
#var/ipfire/main/settings
#var/ipfire/main/manualpages
#var/ipfire/menu.d
var/ipfire/menu.d/00-menu.main
var/ipfire/menu.d/10-system.menu
var/ipfire/menu.d/20-status.menu
var/ipfire/menu.d/30-network.menu
var/ipfire/menu.d/40-services.menu
var/ipfire/menu.d/50-firewall.menu
var/ipfire/menu.d/60-ipfire.menu
var/ipfire/menu.d/70-log.menu
#var/ipfire/menu.d/EX-apcupsd.menu
#var/ipfire/menu.d/EX-guardian.menu
#var/ipfire/menu.d/EX-mpfire.menu
#var/ipfire/menu.d/EX-samba.menu
#var/ipfire/menu.d/EX-tor.menu
#var/ipfire/menu.d/EX-wio.menu
#var/ipfire/menu.d/EX-wlanap.menu
var/ipfire/modem
var/ipfire/modem-lib.pl
#var/ipfire/modem/defaults
#var/ipfire/modem/settings
var/ipfire/modem-lib.pl
var/ipfire/network-functions.pl
var/ipfire/optionsfw
#var/ipfire/optionsfw/settings
var/ipfire/ovpn
#var/ipfire/ovpn/ccd.conf
#var/ipfire/ovpn/ccdroute
#var/ipfire/ovpn/ccdroute2
var/ipfire/pakfire
#var/ipfire/pakfire/settings
#var/ipfire/patches
#var/ipfire/portfw
#var/ipfire/portfw/config
var/ipfire/ppp
#var/ipfire/ppp/fake-resolv.conf
#var/ipfire/ppp/settings
#var/ipfire/ppp/settings-1
#var/ipfire/ppp/settings-2
#var/ipfire/ppp/settings-3
#var/ipfire/ppp/settings-4
#var/ipfire/ppp/settings-5
var/ipfire/private
var/ipfire/proxy
#var/ipfire/proxy/acl-1.4
#var/ipfire/proxy/advanced
#var/ipfire/proxy/advanced/cre
#var/ipfire/proxy/advanced/cre/enable
#var/ipfire/proxy/advanced/settings
#var/ipfire/proxy/calamaris
#var/ipfire/proxy/calamaris/bin
#var/ipfire/proxy/settings
#var/ipfire/proxy/squid.conf
var/ipfire/qos
#var/ipfire/qos/bin
#var/ipfire/qos/bin/RRD-func.pl
#var/ipfire/qos/bin/event-func.pl
#var/ipfire/qos/bin/makeqosscripts.pl
#var/ipfire/qos/bin/migrate.pl
#var/ipfire/qos/bin/parse-func.pl
#var/ipfire/qos/classes
#var/ipfire/qos/level7config
#var/ipfire/qos/portconfig
#var/ipfire/qos/settings
#var/ipfire/qos/subclasses
#var/ipfire/qos/tosconfig
var/ipfire/red
var/ipfire/remote
#var/ipfire/remote/settings
var/ipfire/sensors
#var/ipfire/sensors/settings
var/ipfire/suricata
#var/ipfire/suricata/settings
var/ipfire/time
#var/ipfire/time/settings
var/ipfire/updatexlrator
var/ipfire/updatexlrator/autocheck
var/ipfire/updatexlrator/bin
var/ipfire/urlfilter
#var/ipfire/urlfilter/autoupdate
#var/ipfire/urlfilter/bin
var/ipfire/vpn
#var/ipfire/vpn/caconfig
#var/ipfire/vpn/config
#var/ipfire/vpn/ipsec.conf
#var/ipfire/vpn/ipsec.secrets
#var/ipfire/vpn/settings
var/ipfire/wakeonlan
#var/ipfire/wakeonlan/clients.conf
var/ipfire/wireguard
#var/ipfire/wireguard/peers
#var/ipfire/wireguard/settings
var/ipfire/wireguard-functions.pl
var/ipfire/wireless
#var/ipfire/wireless/config
#var/ipfire/wireless/settings
var/ipfire/ddos
var/ipfire/loxilb
var/ipfire/xdpdns
var/ipfire/xdpsni

#########################################################################
# ids-update script for Logwatch
# Analyzes the IPFire IP Blocklist update log
#
# Version: 1.0.0
#    Initial release
#
#########################################################################

#########################################################################
# This script is subject to the same copyright as Logwatch itself
#########################################################################

#########################################################################
# Files - all shown with default paths:
#
# /usr/share/logwatch/default.conf/logfiles/messages.conf
# /usr/share/logwatch/dist.conf/services/blocklist.conf (this file)
# /usr/share/logwatch/scripts/services/blocklist
#
# ... and of course
#
# /var/log/messages
#########################################################################


Title = "IP Blocklist"

# Which logfile group...
LogFile = messages

*applystddate

# vi: shiftwidth=3 tabstop=3 et

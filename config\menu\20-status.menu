    $substatus->{'10.systemstatus'} = {
				 'caption' => $Lang::tr{'system'},
				 'uri' => '/cgi-bin/system.cgi',
				 'title' => "$Lang::tr{'system'}",
				 'enabled' => 1,
				 };
    $substatus->{'20.memory'} = {
				'caption' => $Lang::tr{'memory'},
				'uri' => '/cgi-bin/memory.cgi',
				'title' => "$Lang::tr{'memory'}",
				'enabled' => 1,
				};
    $substatus->{'30.services'} = {
				'caption' => $Lang::tr{'services'},
				'uri' => '/cgi-bin/services.cgi',
				'title' => "$Lang::tr{'services'}",
				'enabled' => 1,
				};
    $substatus->{'40.media'} = {
				'caption' => $Lang::tr{'media'},
				'uri' => '/cgi-bin/media.cgi',
				'title' => "$Lang::tr{'media'}",
				'enabled' => 1,
				};
    $substatus->{'50.networkred'} = {
				'caption' => "$Lang::tr{'network red'}",
				'uri' => '/cgi-bin/netexternal.cgi',
				'title' => "$Lang::tr{'network red'}",
				'enabled' => 1,
			  };
    $substatus->{'51.networkinternal'} = {
				'caption' => "$Lang::tr{'network internal'}",
				'uri' => '/cgi-bin/netinternal.cgi',
				'title' => "$Lang::tr{'network internal'}",
				'enabled' => 1,
			  };
    $substatus->{'52.networkother'} = {
				'caption' => "$Lang::tr{'network other'}",
				'uri' => '/cgi-bin/netother.cgi',
				'title' => "$Lang::tr{'network other'}",
				'enabled' => 1,
			  };
	$substatus->{'53.networkovpn'} = {
				'caption' => "$Lang::tr{'vpn statistic rw'}",
				'uri' => '/cgi-bin/netovpnrw.cgi',
				'title' => "$Lang::tr{'vpn statistic rw'}",
				'enabled' => 1,
			  };
	$substatus->{'54.networkovpnsrv'} = {
				'caption' => "$Lang::tr{'vpn statistic n2n'}",
				'uri' => '/cgi-bin/netovpnsrv.cgi',
				'title' => "$Lang::tr{'vpn statistic n2n'}",
				'enabled' => 1,
			  };
    $substatus->{'60.hardwaregraphs'} = {
				'caption' => "$Lang::tr{'hardware graphs'}",
				'uri' => '/cgi-bin/hardwaregraphs.cgi',
				'title' => "$Lang::tr{'hardware graphs'}",
				'enabled' => 1,
			  };
    $substatus->{'71.connections'} = {
				'caption' => $Lang::tr{'connections'},
				'uri' => '/cgi-bin/connections.cgi',
				'title' => "$Lang::tr{'connections'}",
				'enabled' => 1,
				};
    $substatus->{'72.nettraf'} = {
				'caption' => $Lang::tr{'sstraffic'},
				'uri' => '/cgi-bin/traffic.cgi',
				'title' => "$Lang::tr{'sstraffic'}",
				'enabled' => 1,
			  };
    $substatus->{'73.qos'} = {
				'caption' => $Lang::tr{'qos graphs'},
				'uri' => '/cgi-bin/qosgraphs.cgi',
				'title' => "$Lang::tr{'qos graphs'}",
				'enabled' => 1,
				};
    $substatus->{'74.modem-status'} = {'caption' => $Lang::tr{'modem status'},
				  'uri' => '/cgi-bin/modem-status.cgi',
				  'title' => $Lang::tr{'modem status'},
				  'enabled' => 0,
				  };
    $substatus->{'75.atm-status'} = {'caption' => 'Atm-status',
				  'uri' => '/cgi-bin/atm-status.cgi',
				  'title' => 'Atm-status',
				  'enabled' => (`find /sys/class/atm/*/device 2>/dev/null` ? 1 : 0),
				  };
    $substatus->{'76.mdstat'} = {'caption' => 'Mdstat',
				  'uri' => '/cgi-bin/mdstat.cgi',
				  'title' => 'Mdstat',
				  'enabled' => 1,
				  };

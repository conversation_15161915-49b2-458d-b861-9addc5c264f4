#
# Config file for collectd(1).
# Please read collectd.conf(5) for a list of options.
# http://collectd.org/
#

Hostname    "localhost"
BaseDir     "/var/log/rrd/collectd"
PIDFile     "/var/run/collectd.pid"
PluginDir   "/usr/lib/collectd"
TypesDB     "/usr/share/collectd/types.db"
Interval     30
ReadThreads  1

LoadPlugin conntrack
LoadPlugin cpu
#LoadPlugin cpufreq
LoadPlugin disk
LoadPlugin interface
LoadPlugin iptables
LoadPlugin load
#LoadPlugin logfile
LoadPlugin memory
LoadPlugin ping
LoadPlugin processes
LoadPlugin rrdtool
LoadPlugin sensors
LoadPlugin swap
LoadPlugin syslog
#LoadPlugin wireless
LoadPlugin match_regex

include "/etc/collectd.precache"

<Plugin interface>
	Interface "lo"
	Interface "/[0-9]*phys$/"
	Interface "/^vnet[0-9]*$/"
	IgnoreSelected true
</Plugin>

<Plugin "disk">
	Disk "/[hs]d[a-z][0-9]$/"
	IgnoreSelected true
</Plugin>

<Plugin iptables>
	Chain filter PSCAN DROP_PScan
	Chain filter NEWNOTSYN DROP_NEWNOTSYN
	Chain filter POLICYFWD DROP_FORWARD
	Chain filter POLICYOUT DROP_OUTPUT
	Chain filter POLICYIN DROP_INPUT
	Chain filter SPOOFED_MARTIAN DROP_SPOOFED_MARTIAN
	Chain filter HOSTILE_DROP_IN DROP_HOSTILE
	Chain filter HOSTILE_DROP_OUT DROP_HOSTILE
</Plugin>

#<Plugin logfile>
#	LogLevel info
#	File STDOUT
#</Plugin>

<Plugin ping>
	Host "gateway"
	interval 30
	timeout 10
</Plugin>

<Plugin processes>
	Process "sshd"
	Process "smbd"
	Process "nmbd"
	Process "squid"
	Process "squidguard"
	Process "charon"
	Process "openvpn"
	Process "qemu"
	Process "mpd"
</Plugin>

<Plugin rrdtool>
	DataDir "/var/log/rrd/collectd"
</Plugin>

<Plugin syslog>
	LogLevel info
</Plugin>

#include "/etc/collectd.thermal"
include "/etc/collectd.custom"
include "/etc/collectd.vpn"

etc/dracut.conf
etc/dracut.conf.d
usr/bin/dracut
usr/bin/dracut-catimages
usr/bin/lsinitrd
#usr/lib/dracut
usr/lib/dracut/dracut-functions
usr/lib/dracut/dracut-functions.sh
usr/lib/dracut/dracut-init.sh
usr/lib/dracut/dracut-initramfs-restore
usr/lib/dracut/dracut-install
usr/lib/dracut/dracut-logger.sh
usr/lib/dracut/dracut-util
usr/lib/dracut/dracut-version.sh
usr/lib/dracut/dracut.conf.d
usr/lib/dracut/dracut.conf.d/ipfire.conf
#usr/lib/dracut/modules.d
usr/lib/dracut/modules.d/00bash
usr/lib/dracut/modules.d/00bash/module-setup.sh
#usr/lib/dracut/modules.d/00dash
#usr/lib/dracut/modules.d/00dash/module-setup.sh
#usr/lib/dracut/modules.d/00mksh
#usr/lib/dracut/modules.d/00mksh/module-setup.sh
#usr/lib/dracut/modules.d/00systemd
#usr/lib/dracut/modules.d/00systemd-network-management
#usr/lib/dracut/modules.d/00systemd-network-management/module-setup.sh
#usr/lib/dracut/modules.d/00systemd/module-setup.sh
usr/lib/dracut/modules.d/00warpclock
usr/lib/dracut/modules.d/00warpclock/module-setup.sh
usr/lib/dracut/modules.d/00warpclock/warpclock.sh
#usr/lib/dracut/modules.d/01fips
#usr/lib/dracut/modules.d/01fips/fips-boot.sh
#usr/lib/dracut/modules.d/01fips/fips-load-crypto.sh
#usr/lib/dracut/modules.d/01fips/fips-noboot.sh
#usr/lib/dracut/modules.d/01fips/fips.sh
#usr/lib/dracut/modules.d/01fips/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-ac-power
#usr/lib/dracut/modules.d/01systemd-ac-power/99-initrd-power-targets.rules
#usr/lib/dracut/modules.d/01systemd-ac-power/initrd-on-ac-power.target
#usr/lib/dracut/modules.d/01systemd-ac-power/initrd-on-battery-power.target
#usr/lib/dracut/modules.d/01systemd-ac-power/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-ask-password
#usr/lib/dracut/modules.d/01systemd-ask-password/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-coredump
#usr/lib/dracut/modules.d/01systemd-coredump/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-hostnamed
#usr/lib/dracut/modules.d/01systemd-hostnamed/99-systemd-networkd-dracut.conf
#usr/lib/dracut/modules.d/01systemd-hostnamed/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-hostnamed/org.freedesktop.hostname1_dracut.conf
#usr/lib/dracut/modules.d/01systemd-hostnamed/systemd-hostname-dracut.conf
#usr/lib/dracut/modules.d/01systemd-initrd
#usr/lib/dracut/modules.d/01systemd-initrd/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-integritysetup
#usr/lib/dracut/modules.d/01systemd-integritysetup/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-journald
#usr/lib/dracut/modules.d/01systemd-journald/initrd.conf
#usr/lib/dracut/modules.d/01systemd-journald/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-ldconfig
#usr/lib/dracut/modules.d/01systemd-ldconfig/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-modules-load
#usr/lib/dracut/modules.d/01systemd-modules-load/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-networkd
#usr/lib/dracut/modules.d/01systemd-networkd/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-repart
#usr/lib/dracut/modules.d/01systemd-repart/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-resolved
#usr/lib/dracut/modules.d/01systemd-resolved/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-resolved/resolved-tmpfile-dracut.conf
#usr/lib/dracut/modules.d/01systemd-rfkill
#usr/lib/dracut/modules.d/01systemd-rfkill/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-sysctl
#usr/lib/dracut/modules.d/01systemd-sysctl/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-sysext
#usr/lib/dracut/modules.d/01systemd-sysext/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-sysusers
#usr/lib/dracut/modules.d/01systemd-sysusers/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-sysusers/sysusers-dracut.conf
#usr/lib/dracut/modules.d/01systemd-timedated
#usr/lib/dracut/modules.d/01systemd-timedated/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-timesyncd
#usr/lib/dracut/modules.d/01systemd-timesyncd/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-timesyncd/timesyncd-tmpfile-dracut.conf
#usr/lib/dracut/modules.d/01systemd-tmpfiles
#usr/lib/dracut/modules.d/01systemd-tmpfiles/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-udevd
#usr/lib/dracut/modules.d/01systemd-udevd/module-setup.sh
#usr/lib/dracut/modules.d/01systemd-veritysetup
#usr/lib/dracut/modules.d/01systemd-veritysetup/module-setup.sh
usr/lib/dracut/modules.d/02caps
usr/lib/dracut/modules.d/02caps/README
usr/lib/dracut/modules.d/02caps/caps.sh
usr/lib/dracut/modules.d/02caps/module-setup.sh
usr/lib/dracut/modules.d/03modsign
usr/lib/dracut/modules.d/03modsign/load-modsign-keys.sh
usr/lib/dracut/modules.d/03modsign/module-setup.sh
usr/lib/dracut/modules.d/03rescue
usr/lib/dracut/modules.d/03rescue/module-setup.sh
#usr/lib/dracut/modules.d/04watchdog
#usr/lib/dracut/modules.d/04watchdog-modules
#usr/lib/dracut/modules.d/04watchdog-modules/module-setup.sh
#usr/lib/dracut/modules.d/04watchdog/module-setup.sh
#usr/lib/dracut/modules.d/04watchdog/watchdog-stop.sh
#usr/lib/dracut/modules.d/04watchdog/watchdog.sh
#usr/lib/dracut/modules.d/05busybox
#usr/lib/dracut/modules.d/05busybox/module-setup.sh
#usr/lib/dracut/modules.d/06dbus-broker
#usr/lib/dracut/modules.d/06dbus-broker/module-setup.sh
#usr/lib/dracut/modules.d/06dbus-daemon
#usr/lib/dracut/modules.d/06dbus-daemon/module-setup.sh
#usr/lib/dracut/modules.d/06rngd
#usr/lib/dracut/modules.d/06rngd/module-setup.sh
#usr/lib/dracut/modules.d/06rngd/rngd.service
usr/lib/dracut/modules.d/09dbus
usr/lib/dracut/modules.d/09dbus/module-setup.sh
usr/lib/dracut/modules.d/10i18n
usr/lib/dracut/modules.d/10i18n/10-console.rules
usr/lib/dracut/modules.d/10i18n/README
usr/lib/dracut/modules.d/10i18n/console_init.sh
usr/lib/dracut/modules.d/10i18n/module-setup.sh
usr/lib/dracut/modules.d/10i18n/parse-i18n.sh
#usr/lib/dracut/modules.d/30convertfs
#usr/lib/dracut/modules.d/30convertfs/convertfs.sh
#usr/lib/dracut/modules.d/30convertfs/do-convertfs.sh
#usr/lib/dracut/modules.d/30convertfs/module-setup.sh
#usr/lib/dracut/modules.d/35network-legacy
#usr/lib/dracut/modules.d/35network-legacy/dhclient-script.sh
#usr/lib/dracut/modules.d/35network-legacy/dhclient.conf
#usr/lib/dracut/modules.d/35network-legacy/dhcp-multi.sh
#usr/lib/dracut/modules.d/35network-legacy/ifup.sh
#usr/lib/dracut/modules.d/35network-legacy/kill-dhclient.sh
#usr/lib/dracut/modules.d/35network-legacy/module-setup.sh
#usr/lib/dracut/modules.d/35network-legacy/net-genrules.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-bond.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-bridge.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-ibft.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-ifname.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-ip-opts.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-team.sh
#usr/lib/dracut/modules.d/35network-legacy/parse-vlan.sh
#usr/lib/dracut/modules.d/35network-manager
#usr/lib/dracut/modules.d/35network-manager/initrd-no-auto-default.conf
#usr/lib/dracut/modules.d/35network-manager/module-setup.sh
#usr/lib/dracut/modules.d/35network-manager/nm-config.sh
#usr/lib/dracut/modules.d/35network-manager/nm-initrd.service
#usr/lib/dracut/modules.d/35network-manager/nm-lib.sh
#usr/lib/dracut/modules.d/35network-manager/nm-run.sh
#usr/lib/dracut/modules.d/35network-manager/nm-wait-online-initrd.service
#usr/lib/dracut/modules.d/35network-wicked
#usr/lib/dracut/modules.d/35network-wicked/module-setup.sh
#usr/lib/dracut/modules.d/35network-wicked/wicked-config.sh
#usr/lib/dracut/modules.d/35network-wicked/wicked-run.sh
#usr/lib/dracut/modules.d/40network
#usr/lib/dracut/modules.d/40network/dhcp-root.sh
#usr/lib/dracut/modules.d/40network/ifname-genrules.sh
#usr/lib/dracut/modules.d/40network/module-setup.sh
#usr/lib/dracut/modules.d/40network/net-lib.sh
#usr/lib/dracut/modules.d/40network/netroot.sh
#usr/lib/dracut/modules.d/45ifcfg
#usr/lib/dracut/modules.d/45ifcfg/module-setup.sh
#usr/lib/dracut/modules.d/45ifcfg/write-ifcfg.sh
#usr/lib/dracut/modules.d/45url-lib
#usr/lib/dracut/modules.d/45url-lib/module-setup.sh
#usr/lib/dracut/modules.d/45url-lib/url-lib.sh
usr/lib/dracut/modules.d/50drm
usr/lib/dracut/modules.d/50drm/module-setup.sh
#usr/lib/dracut/modules.d/50gensplash
#usr/lib/dracut/modules.d/50gensplash/README
#usr/lib/dracut/modules.d/50gensplash/gensplash-emergency.sh
#usr/lib/dracut/modules.d/50gensplash/gensplash-newroot.sh
#usr/lib/dracut/modules.d/50gensplash/gensplash-pretrigger.sh
#usr/lib/dracut/modules.d/50gensplash/module-setup.sh
#usr/lib/dracut/modules.d/50plymouth
#usr/lib/dracut/modules.d/50plymouth/module-setup.sh
#usr/lib/dracut/modules.d/50plymouth/plymouth-emergency.sh
#usr/lib/dracut/modules.d/50plymouth/plymouth-newroot.sh
#usr/lib/dracut/modules.d/50plymouth/plymouth-populate-initrd.sh
#usr/lib/dracut/modules.d/50plymouth/plymouth-pretrigger.sh
#usr/lib/dracut/modules.d/62bluetooth
#usr/lib/dracut/modules.d/62bluetooth/module-setup.sh
#usr/lib/dracut/modules.d/80cms
#usr/lib/dracut/modules.d/80cms/cms-write-ifcfg.sh
#usr/lib/dracut/modules.d/80cms/cmsifup.sh
#usr/lib/dracut/modules.d/80cms/cmssetup.sh
#usr/lib/dracut/modules.d/80cms/module-setup.sh
#usr/lib/dracut/modules.d/80lvmmerge
#usr/lib/dracut/modules.d/80lvmmerge/README.md
#usr/lib/dracut/modules.d/80lvmmerge/lvmmerge.sh
#usr/lib/dracut/modules.d/80lvmmerge/module-setup.sh
#usr/lib/dracut/modules.d/81cio_ignore
#usr/lib/dracut/modules.d/81cio_ignore/module-setup.sh
#usr/lib/dracut/modules.d/81cio_ignore/parse-cio_accept.sh
#usr/lib/dracut/modules.d/90btrfs
#usr/lib/dracut/modules.d/90btrfs/80-btrfs.rules
#usr/lib/dracut/modules.d/90btrfs/btrfs_device_ready.sh
#usr/lib/dracut/modules.d/90btrfs/btrfs_finished.sh
#usr/lib/dracut/modules.d/90btrfs/btrfs_timeout.sh
#usr/lib/dracut/modules.d/90btrfs/module-setup.sh
#usr/lib/dracut/modules.d/90crypt
#usr/lib/dracut/modules.d/90crypt/crypt-cleanup.sh
#usr/lib/dracut/modules.d/90crypt/crypt-lib.sh
#usr/lib/dracut/modules.d/90crypt/crypt-run-generator.sh
#usr/lib/dracut/modules.d/90crypt/cryptroot-ask.sh
#usr/lib/dracut/modules.d/90crypt/module-setup.sh
#usr/lib/dracut/modules.d/90crypt/parse-crypt.sh
#usr/lib/dracut/modules.d/90crypt/parse-keydev.sh
#usr/lib/dracut/modules.d/90crypt/probe-keydev.sh
usr/lib/dracut/modules.d/90dm
usr/lib/dracut/modules.d/90dm/11-dm.rules
usr/lib/dracut/modules.d/90dm/59-persistent-storage-dm.rules
usr/lib/dracut/modules.d/90dm/dm-pre-udev.sh
usr/lib/dracut/modules.d/90dm/dm-shutdown.sh
usr/lib/dracut/modules.d/90dm/module-setup.sh
#usr/lib/dracut/modules.d/90dmraid
#usr/lib/dracut/modules.d/90dmraid/61-dmraid-imsm.rules
#usr/lib/dracut/modules.d/90dmraid/dmraid.sh
#usr/lib/dracut/modules.d/90dmraid/module-setup.sh
#usr/lib/dracut/modules.d/90dmraid/parse-dm.sh
#usr/lib/dracut/modules.d/90dmsquash-live
#usr/lib/dracut/modules.d/90dmsquash-live-ntfs
#usr/lib/dracut/modules.d/90dmsquash-live-ntfs/module-setup.sh
#usr/lib/dracut/modules.d/90dmsquash-live/apply-live-updates.sh
#usr/lib/dracut/modules.d/90dmsquash-live/checkisomd5@.service
#usr/lib/dracut/modules.d/90dmsquash-live/dmsquash-generator.sh
#usr/lib/dracut/modules.d/90dmsquash-live/dmsquash-live-genrules.sh
#usr/lib/dracut/modules.d/90dmsquash-live/dmsquash-live-root.sh
#usr/lib/dracut/modules.d/90dmsquash-live/dmsquash-liveiso-genrules.sh
#usr/lib/dracut/modules.d/90dmsquash-live/iso-scan.sh
#usr/lib/dracut/modules.d/90dmsquash-live/module-setup.sh
#usr/lib/dracut/modules.d/90dmsquash-live/parse-dmsquash-live.sh
#usr/lib/dracut/modules.d/90dmsquash-live/parse-iso-scan.sh
usr/lib/dracut/modules.d/90kernel-modules
usr/lib/dracut/modules.d/90kernel-modules-extra
usr/lib/dracut/modules.d/90kernel-modules-extra/module-setup.sh
usr/lib/dracut/modules.d/90kernel-modules/insmodpost.sh
usr/lib/dracut/modules.d/90kernel-modules/module-setup.sh
usr/lib/dracut/modules.d/90kernel-modules/parse-kernel.sh
#usr/lib/dracut/modules.d/90kernel-network-modules
#usr/lib/dracut/modules.d/90kernel-network-modules/module-setup.sh
#usr/lib/dracut/modules.d/90livenet
#usr/lib/dracut/modules.d/90livenet/fetch-liveupdate.sh
#usr/lib/dracut/modules.d/90livenet/livenet-generator.sh
#usr/lib/dracut/modules.d/90livenet/livenetroot.sh
#usr/lib/dracut/modules.d/90livenet/module-setup.sh
#usr/lib/dracut/modules.d/90livenet/parse-livenet.sh
usr/lib/dracut/modules.d/90lvm
usr/lib/dracut/modules.d/90lvm/64-lvm.rules
usr/lib/dracut/modules.d/90lvm/lvm_scan.sh
usr/lib/dracut/modules.d/90lvm/module-setup.sh
usr/lib/dracut/modules.d/90lvm/parse-lvm.sh
usr/lib/dracut/modules.d/90mdraid
usr/lib/dracut/modules.d/90mdraid/59-persistent-storage-md.rules
usr/lib/dracut/modules.d/90mdraid/65-md-incremental-imsm.rules
usr/lib/dracut/modules.d/90mdraid/md-shutdown.sh
usr/lib/dracut/modules.d/90mdraid/mdmon-pre-shutdown.sh
usr/lib/dracut/modules.d/90mdraid/mdmon-pre-udev.sh
usr/lib/dracut/modules.d/90mdraid/mdraid-cleanup.sh
usr/lib/dracut/modules.d/90mdraid/mdraid-needshutdown.sh
usr/lib/dracut/modules.d/90mdraid/mdraid-waitclean.sh
usr/lib/dracut/modules.d/90mdraid/mdraid_start.sh
usr/lib/dracut/modules.d/90mdraid/module-setup.sh
usr/lib/dracut/modules.d/90mdraid/parse-md.sh
#usr/lib/dracut/modules.d/90multipath
#usr/lib/dracut/modules.d/90multipath/module-setup.sh
#usr/lib/dracut/modules.d/90multipath/multipath-shutdown.sh
#usr/lib/dracut/modules.d/90multipath/multipathd-configure.service
#usr/lib/dracut/modules.d/90multipath/multipathd-needshutdown.sh
#usr/lib/dracut/modules.d/90multipath/multipathd-stop.sh
#usr/lib/dracut/modules.d/90multipath/multipathd.service
#usr/lib/dracut/modules.d/90multipath/multipathd.sh
#usr/lib/dracut/modules.d/90nvdimm
#usr/lib/dracut/modules.d/90nvdimm/module-setup.sh
#usr/lib/dracut/modules.d/90ppcmac
#usr/lib/dracut/modules.d/90ppcmac/load-thermal.sh
#usr/lib/dracut/modules.d/90ppcmac/module-setup.sh
usr/lib/dracut/modules.d/90qemu
#usr/lib/dracut/modules.d/90qemu-net
#usr/lib/dracut/modules.d/90qemu-net/module-setup.sh
usr/lib/dracut/modules.d/90qemu/module-setup.sh
#usr/lib/dracut/modules.d/91crypt-gpg
#usr/lib/dracut/modules.d/91crypt-gpg/README
#usr/lib/dracut/modules.d/91crypt-gpg/crypt-gpg-lib.sh
#usr/lib/dracut/modules.d/91crypt-gpg/module-setup.sh
#usr/lib/dracut/modules.d/91crypt-loop
#usr/lib/dracut/modules.d/91crypt-loop/crypt-loop-lib.sh
#usr/lib/dracut/modules.d/91crypt-loop/module-setup.sh
#usr/lib/dracut/modules.d/91fido2
#usr/lib/dracut/modules.d/91fido2/module-setup.sh
#usr/lib/dracut/modules.d/91pcsc
#usr/lib/dracut/modules.d/91pcsc/module-setup.sh
#usr/lib/dracut/modules.d/91pcsc/pcscd.service
#usr/lib/dracut/modules.d/91pcsc/pcscd.socket
#usr/lib/dracut/modules.d/91pkcs11
#usr/lib/dracut/modules.d/91pkcs11/module-setup.sh
#usr/lib/dracut/modules.d/91tpm2-tss
#usr/lib/dracut/modules.d/91tpm2-tss/module-setup.sh
#usr/lib/dracut/modules.d/91zipl
#usr/lib/dracut/modules.d/91zipl/install_zipl_cmdline.sh
#usr/lib/dracut/modules.d/91zipl/module-setup.sh
#usr/lib/dracut/modules.d/91zipl/parse-zipl.sh
#usr/lib/dracut/modules.d/95cifs
#usr/lib/dracut/modules.d/95cifs/cifs-lib.sh
#usr/lib/dracut/modules.d/95cifs/cifsroot.sh
#usr/lib/dracut/modules.d/95cifs/module-setup.sh
#usr/lib/dracut/modules.d/95cifs/parse-cifsroot.sh
#usr/lib/dracut/modules.d/95dasd
#usr/lib/dracut/modules.d/95dasd/module-setup.sh
#usr/lib/dracut/modules.d/95dasd/parse-dasd.sh
#usr/lib/dracut/modules.d/95dasd_mod
#usr/lib/dracut/modules.d/95dasd_mod/module-setup.sh
#usr/lib/dracut/modules.d/95dasd_mod/parse-dasd-mod.sh
#usr/lib/dracut/modules.d/95dasd_rules
#usr/lib/dracut/modules.d/95dasd_rules/module-setup.sh
#usr/lib/dracut/modules.d/95dasd_rules/parse-dasd.sh
#usr/lib/dracut/modules.d/95dcssblk
#usr/lib/dracut/modules.d/95dcssblk/module-setup.sh
#usr/lib/dracut/modules.d/95dcssblk/parse-dcssblk.sh
#usr/lib/dracut/modules.d/95debug
#usr/lib/dracut/modules.d/95debug/module-setup.sh
#usr/lib/dracut/modules.d/95fcoe
#usr/lib/dracut/modules.d/95fcoe-uefi
#usr/lib/dracut/modules.d/95fcoe-uefi/module-setup.sh
#usr/lib/dracut/modules.d/95fcoe-uefi/parse-uefifcoe.sh
#usr/lib/dracut/modules.d/95fcoe/cleanup-fcoe.sh
#usr/lib/dracut/modules.d/95fcoe/fcoe-edd.sh
#usr/lib/dracut/modules.d/95fcoe/fcoe-up.sh
#usr/lib/dracut/modules.d/95fcoe/lldpad.sh
#usr/lib/dracut/modules.d/95fcoe/module-setup.sh
#usr/lib/dracut/modules.d/95fcoe/parse-fcoe.sh
#usr/lib/dracut/modules.d/95fcoe/stop-fcoe.sh
#usr/lib/dracut/modules.d/95fstab-sys
#usr/lib/dracut/modules.d/95fstab-sys/module-setup.sh
#usr/lib/dracut/modules.d/95fstab-sys/mount-sys.sh
#usr/lib/dracut/modules.d/95iscsi
#usr/lib/dracut/modules.d/95iscsi/cleanup-iscsi.sh
#usr/lib/dracut/modules.d/95iscsi/iscsiroot.sh
#usr/lib/dracut/modules.d/95iscsi/module-setup.sh
#usr/lib/dracut/modules.d/95iscsi/mount-lun.sh
#usr/lib/dracut/modules.d/95iscsi/parse-iscsiroot.sh
#usr/lib/dracut/modules.d/95lunmask
#usr/lib/dracut/modules.d/95lunmask/fc_transport_scan_lun.sh
#usr/lib/dracut/modules.d/95lunmask/module-setup.sh
#usr/lib/dracut/modules.d/95lunmask/parse-lunmask.sh
#usr/lib/dracut/modules.d/95lunmask/sas_transport_scan_lun.sh
#usr/lib/dracut/modules.d/95nbd
#usr/lib/dracut/modules.d/95nbd/module-setup.sh
#usr/lib/dracut/modules.d/95nbd/nbd-generator.sh
#usr/lib/dracut/modules.d/95nbd/nbdroot.sh
#usr/lib/dracut/modules.d/95nbd/parse-nbdroot.sh
#usr/lib/dracut/modules.d/95nfs
#usr/lib/dracut/modules.d/95nfs/module-setup.sh
#usr/lib/dracut/modules.d/95nfs/nfs-lib.sh
#usr/lib/dracut/modules.d/95nfs/nfs-start-rpc.sh
#usr/lib/dracut/modules.d/95nfs/nfsroot-cleanup.sh
#usr/lib/dracut/modules.d/95nfs/nfsroot.sh
#usr/lib/dracut/modules.d/95nfs/parse-nfsroot.sh
#usr/lib/dracut/modules.d/95nvmf
#usr/lib/dracut/modules.d/95nvmf/95-nvmf-initqueue.rules
#usr/lib/dracut/modules.d/95nvmf/module-setup.sh
#usr/lib/dracut/modules.d/95nvmf/nvmf-autoconnect.sh
#usr/lib/dracut/modules.d/95nvmf/parse-nvmf-boot-connections.sh
#usr/lib/dracut/modules.d/95qeth_rules
#usr/lib/dracut/modules.d/95qeth_rules/module-setup.sh
#usr/lib/dracut/modules.d/95resume
#usr/lib/dracut/modules.d/95resume/module-setup.sh
#usr/lib/dracut/modules.d/95resume/parse-resume.sh
#usr/lib/dracut/modules.d/95resume/resume.sh
usr/lib/dracut/modules.d/95rootfs-block
usr/lib/dracut/modules.d/95rootfs-block/block-genrules.sh
usr/lib/dracut/modules.d/95rootfs-block/module-setup.sh
usr/lib/dracut/modules.d/95rootfs-block/mount-root.sh
usr/lib/dracut/modules.d/95rootfs-block/parse-block.sh
usr/lib/dracut/modules.d/95rootfs-block/rootfallback.sh
#usr/lib/dracut/modules.d/95ssh-client
#usr/lib/dracut/modules.d/95ssh-client/module-setup.sh
usr/lib/dracut/modules.d/95terminfo
usr/lib/dracut/modules.d/95terminfo/module-setup.sh
usr/lib/dracut/modules.d/95udev-rules
usr/lib/dracut/modules.d/95udev-rules/59-persistent-storage.rules
usr/lib/dracut/modules.d/95udev-rules/61-persistent-storage.rules
usr/lib/dracut/modules.d/95udev-rules/load-modules.sh
usr/lib/dracut/modules.d/95udev-rules/module-setup.sh
#usr/lib/dracut/modules.d/95virtfs
#usr/lib/dracut/modules.d/95virtfs/module-setup.sh
#usr/lib/dracut/modules.d/95virtfs/mount-virtfs.sh
#usr/lib/dracut/modules.d/95virtfs/parse-virtfs.sh
#usr/lib/dracut/modules.d/95zfcp
#usr/lib/dracut/modules.d/95zfcp/module-setup.sh
#usr/lib/dracut/modules.d/95zfcp/parse-zfcp.sh
#usr/lib/dracut/modules.d/95zfcp_rules
#usr/lib/dracut/modules.d/95zfcp_rules/module-setup.sh
#usr/lib/dracut/modules.d/95zfcp_rules/parse-zfcp.sh
#usr/lib/dracut/modules.d/95znet
#usr/lib/dracut/modules.d/95znet/module-setup.sh
#usr/lib/dracut/modules.d/95znet/parse-ccw.sh
#usr/lib/dracut/modules.d/96securityfs
#usr/lib/dracut/modules.d/96securityfs/module-setup.sh
#usr/lib/dracut/modules.d/96securityfs/securityfs.sh
#usr/lib/dracut/modules.d/97biosdevname
#usr/lib/dracut/modules.d/97biosdevname/module-setup.sh
#usr/lib/dracut/modules.d/97biosdevname/parse-biosdevname.sh
#usr/lib/dracut/modules.d/97masterkey
#usr/lib/dracut/modules.d/97masterkey/README
#usr/lib/dracut/modules.d/97masterkey/masterkey.sh
#usr/lib/dracut/modules.d/97masterkey/module-setup.sh
#usr/lib/dracut/modules.d/98dracut-systemd
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline-ask.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline-ask.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-cmdline.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-emergency.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-emergency.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-initqueue.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-initqueue.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-initqueue.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-initqueue.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-mount.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-mount.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-mount.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-mount.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-mount.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-mount.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-mount.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-mount.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-pivot.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-pivot.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-pivot.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-pivot.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-trigger.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-trigger.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-trigger.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-trigger.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-udev.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-udev.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-udev.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-pre-udev.sh
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-shutdown-onfailure.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-shutdown.service
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-shutdown.service.8
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-shutdown.service.8.asc
#usr/lib/dracut/modules.d/98dracut-systemd/dracut-tmpfiles.conf
#usr/lib/dracut/modules.d/98dracut-systemd/emergency.service
#usr/lib/dracut/modules.d/98dracut-systemd/module-setup.sh
#usr/lib/dracut/modules.d/98dracut-systemd/rootfs-generator.sh
#usr/lib/dracut/modules.d/98ecryptfs
#usr/lib/dracut/modules.d/98ecryptfs/README
#usr/lib/dracut/modules.d/98ecryptfs/ecryptfs-mount.sh
#usr/lib/dracut/modules.d/98ecryptfs/module-setup.sh
#usr/lib/dracut/modules.d/98integrity
#usr/lib/dracut/modules.d/98integrity/README
#usr/lib/dracut/modules.d/98integrity/evm-enable.sh
#usr/lib/dracut/modules.d/98integrity/ima-keys-load.sh
#usr/lib/dracut/modules.d/98integrity/ima-policy-load.sh
#usr/lib/dracut/modules.d/98integrity/module-setup.sh
#usr/lib/dracut/modules.d/98pollcdrom
#usr/lib/dracut/modules.d/98pollcdrom/module-setup.sh
#usr/lib/dracut/modules.d/98pollcdrom/pollcdrom.sh
#usr/lib/dracut/modules.d/98selinux
#usr/lib/dracut/modules.d/98selinux/module-setup.sh
#usr/lib/dracut/modules.d/98selinux/selinux-loadpolicy.sh
#usr/lib/dracut/modules.d/98syslog
#usr/lib/dracut/modules.d/98syslog/README
#usr/lib/dracut/modules.d/98syslog/module-setup.sh
#usr/lib/dracut/modules.d/98syslog/parse-syslog-opts.sh
#usr/lib/dracut/modules.d/98syslog/rsyslog.conf
#usr/lib/dracut/modules.d/98syslog/rsyslogd-start.sh
#usr/lib/dracut/modules.d/98syslog/rsyslogd-stop.sh
#usr/lib/dracut/modules.d/98syslog/syslog-cleanup.sh
#usr/lib/dracut/modules.d/98usrmount
#usr/lib/dracut/modules.d/98usrmount/module-setup.sh
#usr/lib/dracut/modules.d/98usrmount/mount-usr.sh
usr/lib/dracut/modules.d/99base
usr/lib/dracut/modules.d/99base/dracut-dev-lib.sh
usr/lib/dracut/modules.d/99base/dracut-lib.sh
usr/lib/dracut/modules.d/99base/init.sh
usr/lib/dracut/modules.d/99base/initqueue.sh
usr/lib/dracut/modules.d/99base/loginit.sh
usr/lib/dracut/modules.d/99base/module-setup.sh
usr/lib/dracut/modules.d/99base/parse-root-opts.sh
usr/lib/dracut/modules.d/99base/rdsosreport.sh
usr/lib/dracut/modules.d/99fs-lib
usr/lib/dracut/modules.d/99fs-lib/fs-lib.sh
usr/lib/dracut/modules.d/99fs-lib/module-setup.sh
#usr/lib/dracut/modules.d/99img-lib
#usr/lib/dracut/modules.d/99img-lib/img-lib.sh
#usr/lib/dracut/modules.d/99img-lib/module-setup.sh
#usr/lib/dracut/modules.d/99memstrack
#usr/lib/dracut/modules.d/99memstrack/memstrack-report.sh
#usr/lib/dracut/modules.d/99memstrack/memstrack-start.sh
#usr/lib/dracut/modules.d/99memstrack/memstrack.service
#usr/lib/dracut/modules.d/99memstrack/module-setup.sh
#usr/lib/dracut/modules.d/99shutdown
#usr/lib/dracut/modules.d/99shutdown/module-setup.sh
#usr/lib/dracut/modules.d/99shutdown/shutdown.sh
#usr/lib/dracut/modules.d/99squash
#usr/lib/dracut/modules.d/99squash/init-squash.sh
#usr/lib/dracut/modules.d/99squash/module-setup.sh
#usr/lib/dracut/modules.d/99uefi-lib
#usr/lib/dracut/modules.d/99uefi-lib/module-setup.sh
#usr/lib/dracut/modules.d/99uefi-lib/uefi-lib.sh
usr/lib/dracut/skipcpio
#usr/lib/kernel
#usr/lib/kernel/install.d
#usr/lib/kernel/install.d/50-dracut.install
#usr/lib/kernel/install.d/51-dracut-rescue.install
#usr/share/bash-completion/completions/dracut
#usr/share/bash-completion/completions/lsinitrd
#usr/share/man/man1/lsinitrd.1
#usr/share/man/man5/dracut.conf.5
#usr/share/man/man7/dracut.bootup.7
#usr/share/man/man7/dracut.cmdline.7
#usr/share/man/man7/dracut.kernel.7
#usr/share/man/man7/dracut.modules.7
#usr/share/man/man8/dracut-catimages.8
#usr/share/man/man8/dracut-cmdline.service.8
#usr/share/man/man8/dracut-initqueue.service.8
#usr/share/man/man8/dracut-mount.service.8
#usr/share/man/man8/dracut-pre-mount.service.8
#usr/share/man/man8/dracut-pre-pivot.service.8
#usr/share/man/man8/dracut-pre-trigger.service.8
#usr/share/man/man8/dracut-pre-udev.service.8
#usr/share/man/man8/dracut-shutdown.service.8
#usr/share/man/man8/dracut.8
#usr/share/pkgconfig/dracut.pc

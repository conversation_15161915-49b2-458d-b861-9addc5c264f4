if not inConfigCheck() then
  xsk = newXsk({ifName='green0', NIC_queue_id=0, frameNums=65536, xskMapPath='/sys/fs/bpf/dnsdist/xsk_map'})
  addLocal("0.0.0.0:53", {xskSocket=xsk})
else
  addLocal("0.0.0.0:53")
end

newServer("*******:53")
newServer("*******:53")

---- newServer({address="*******", healthCheckMode='lazy', checkInterval=1, lazyHealthCheckFailedInterval=30, rise=2, maxCheckFailures=3, lazyHealthCheckThreshold=30, lazyHealthCheckSampleSize=100,  lazyHealthCheckMinSampleCount=10, lazyHealthCheckMode='TimeoutOnly'})

---- newServer({address="*******", healthCheckMode='lazy', checkInterval=1, lazyHealthCheckFailedInterval=30, rise=2, maxCheckFailures=3, lazyHealthCheckThreshold=30, lazyHealthCheckSampleSize=100,  lazyHealthCheckMinSampleCount=10, lazyHealthCheckMode='TimeoutOnly'})



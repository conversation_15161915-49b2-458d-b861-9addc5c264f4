# Log all kernel messages to the console.
# Logging much else clutters up the screen.
#kern.*							/dev/console

# Log anything (except mail) of level info or higher.
# Don't log private authentication messages!
# local0.* any dhcpcd log (even debug) in messages
cron.none;daemon.*;local0.*;local2.*;local5.*;*.info;mail.none;authpriv.*	-/var/log/messages

# Log crons
#cron.*										-/var/log/cron.log

# Everybody gets emergency messages
*.emerg										*

# Display logs on tty12
*.*												/dev/tty12

# Optionally log to a remote host
#*.*											@hostname.domain

# Postfix logs
mail.*										-/var/log/mail

# HAProxy
local1.*								-/var/log/haproxy

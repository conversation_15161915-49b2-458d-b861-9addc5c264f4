#usr/bin/gapplication
#usr/bin/gdbus
#usr/bin/gdbus-codegen
#usr/bin/gio
#usr/bin/gio-querymodules
#usr/bin/glib-compile-resources
#usr/bin/glib-compile-schemas
#usr/bin/glib-genmarshal
#usr/bin/glib-gettextize
#usr/bin/glib-mkenums
#usr/bin/gobject-query
#usr/bin/gresource
#usr/bin/gsettings
#usr/bin/gtester
#usr/bin/gtester-report
#usr/include/gio-unix-2.0
#usr/include/gio-unix-2.0/gio
#usr/include/gio-unix-2.0/gio/gdesktopappinfo.h
#usr/include/gio-unix-2.0/gio/gfiledescriptorbased.h
#usr/include/gio-unix-2.0/gio/gunixfdmessage.h
#usr/include/gio-unix-2.0/gio/gunixinputstream.h
#usr/include/gio-unix-2.0/gio/gunixmounts.h
#usr/include/gio-unix-2.0/gio/gunixoutputstream.h
#usr/include/glib-2.0
#usr/include/glib-2.0/gio
#usr/include/glib-2.0/gio/gaction.h
#usr/include/glib-2.0/gio/gactiongroup.h
#usr/include/glib-2.0/gio/gactiongroupexporter.h
#usr/include/glib-2.0/gio/gactionmap.h
#usr/include/glib-2.0/gio/gappinfo.h
#usr/include/glib-2.0/gio/gapplication.h
#usr/include/glib-2.0/gio/gapplicationcommandline.h
#usr/include/glib-2.0/gio/gasyncinitable.h
#usr/include/glib-2.0/gio/gasyncresult.h
#usr/include/glib-2.0/gio/gbufferedinputstream.h
#usr/include/glib-2.0/gio/gbufferedoutputstream.h
#usr/include/glib-2.0/gio/gbytesicon.h
#usr/include/glib-2.0/gio/gcancellable.h
#usr/include/glib-2.0/gio/gcharsetconverter.h
#usr/include/glib-2.0/gio/gcontenttype.h
#usr/include/glib-2.0/gio/gconverter.h
#usr/include/glib-2.0/gio/gconverterinputstream.h
#usr/include/glib-2.0/gio/gconverteroutputstream.h
#usr/include/glib-2.0/gio/gcredentials.h
#usr/include/glib-2.0/gio/gdatagrambased.h
#usr/include/glib-2.0/gio/gdatainputstream.h
#usr/include/glib-2.0/gio/gdataoutputstream.h
#usr/include/glib-2.0/gio/gdbusactiongroup.h
#usr/include/glib-2.0/gio/gdbusaddress.h
#usr/include/glib-2.0/gio/gdbusauthobserver.h
#usr/include/glib-2.0/gio/gdbusconnection.h
#usr/include/glib-2.0/gio/gdbuserror.h
#usr/include/glib-2.0/gio/gdbusinterface.h
#usr/include/glib-2.0/gio/gdbusinterfaceskeleton.h
#usr/include/glib-2.0/gio/gdbusintrospection.h
#usr/include/glib-2.0/gio/gdbusmenumodel.h
#usr/include/glib-2.0/gio/gdbusmessage.h
#usr/include/glib-2.0/gio/gdbusmethodinvocation.h
#usr/include/glib-2.0/gio/gdbusnameowning.h
#usr/include/glib-2.0/gio/gdbusnamewatching.h
#usr/include/glib-2.0/gio/gdbusobject.h
#usr/include/glib-2.0/gio/gdbusobjectmanager.h
#usr/include/glib-2.0/gio/gdbusobjectmanagerclient.h
#usr/include/glib-2.0/gio/gdbusobjectmanagerserver.h
#usr/include/glib-2.0/gio/gdbusobjectproxy.h
#usr/include/glib-2.0/gio/gdbusobjectskeleton.h
#usr/include/glib-2.0/gio/gdbusproxy.h
#usr/include/glib-2.0/gio/gdbusserver.h
#usr/include/glib-2.0/gio/gdbusutils.h
usr/include/glib-2.0/gio/gdebugcontroller.h
#usr/include/glib-2.0/gio/gdebugcontrollerdbus.h
#usr/include/glib-2.0/gio/gdrive.h
#usr/include/glib-2.0/gio/gdtlsclientconnection.h
#usr/include/glib-2.0/gio/gdtlsconnection.h
#usr/include/glib-2.0/gio/gdtlsserverconnection.h
#usr/include/glib-2.0/gio/gemblem.h
#usr/include/glib-2.0/gio/gemblemedicon.h
#usr/include/glib-2.0/gio/gfile.h
#usr/include/glib-2.0/gio/gfileattribute.h
#usr/include/glib-2.0/gio/gfileenumerator.h
#usr/include/glib-2.0/gio/gfileicon.h
#usr/include/glib-2.0/gio/gfileinfo.h
#usr/include/glib-2.0/gio/gfileinputstream.h
#usr/include/glib-2.0/gio/gfileiostream.h
#usr/include/glib-2.0/gio/gfilemonitor.h
#usr/include/glib-2.0/gio/gfilenamecompleter.h
#usr/include/glib-2.0/gio/gfileoutputstream.h
#usr/include/glib-2.0/gio/gfilterinputstream.h
#usr/include/glib-2.0/gio/gfilteroutputstream.h
#usr/include/glib-2.0/gio/gicon.h
#usr/include/glib-2.0/gio/ginetaddress.h
#usr/include/glib-2.0/gio/ginetaddressmask.h
#usr/include/glib-2.0/gio/ginetsocketaddress.h
#usr/include/glib-2.0/gio/ginitable.h
#usr/include/glib-2.0/gio/ginputstream.h
#usr/include/glib-2.0/gio/gio-autocleanups.h
#usr/include/glib-2.0/gio/gio-visibility.h
#usr/include/glib-2.0/gio/gio.h
#usr/include/glib-2.0/gio/gioenums.h
#usr/include/glib-2.0/gio/gioenumtypes.h
#usr/include/glib-2.0/gio/gioerror.h
#usr/include/glib-2.0/gio/giomodule.h
#usr/include/glib-2.0/gio/gioscheduler.h
#usr/include/glib-2.0/gio/giostream.h
#usr/include/glib-2.0/gio/giotypes.h
#usr/include/glib-2.0/gio/glistmodel.h
#usr/include/glib-2.0/gio/gliststore.h
#usr/include/glib-2.0/gio/gloadableicon.h
#usr/include/glib-2.0/gio/gmemoryinputstream.h
#usr/include/glib-2.0/gio/gmemorymonitor.h
#usr/include/glib-2.0/gio/gmemoryoutputstream.h
#usr/include/glib-2.0/gio/gmenu.h
#usr/include/glib-2.0/gio/gmenuexporter.h
#usr/include/glib-2.0/gio/gmenumodel.h
#usr/include/glib-2.0/gio/gmount.h
#usr/include/glib-2.0/gio/gmountoperation.h
#usr/include/glib-2.0/gio/gnativesocketaddress.h
#usr/include/glib-2.0/gio/gnativevolumemonitor.h
#usr/include/glib-2.0/gio/gnetworkaddress.h
#usr/include/glib-2.0/gio/gnetworking.h
#usr/include/glib-2.0/gio/gnetworkmonitor.h
#usr/include/glib-2.0/gio/gnetworkservice.h
#usr/include/glib-2.0/gio/gnotification.h
#usr/include/glib-2.0/gio/goutputstream.h
#usr/include/glib-2.0/gio/gpermission.h
#usr/include/glib-2.0/gio/gpollableinputstream.h
#usr/include/glib-2.0/gio/gpollableoutputstream.h
#usr/include/glib-2.0/gio/gpollableutils.h
#usr/include/glib-2.0/gio/gpowerprofilemonitor.h
#usr/include/glib-2.0/gio/gpropertyaction.h
#usr/include/glib-2.0/gio/gproxy.h
#usr/include/glib-2.0/gio/gproxyaddress.h
#usr/include/glib-2.0/gio/gproxyaddressenumerator.h
#usr/include/glib-2.0/gio/gproxyresolver.h
#usr/include/glib-2.0/gio/gremoteactiongroup.h
#usr/include/glib-2.0/gio/gresolver.h
#usr/include/glib-2.0/gio/gresource.h
#usr/include/glib-2.0/gio/gseekable.h
#usr/include/glib-2.0/gio/gsettings.h
#usr/include/glib-2.0/gio/gsettingsbackend.h
#usr/include/glib-2.0/gio/gsettingsschema.h
#usr/include/glib-2.0/gio/gsimpleaction.h
#usr/include/glib-2.0/gio/gsimpleactiongroup.h
#usr/include/glib-2.0/gio/gsimpleasyncresult.h
#usr/include/glib-2.0/gio/gsimpleiostream.h
#usr/include/glib-2.0/gio/gsimplepermission.h
#usr/include/glib-2.0/gio/gsimpleproxyresolver.h
#usr/include/glib-2.0/gio/gsocket.h
#usr/include/glib-2.0/gio/gsocketaddress.h
#usr/include/glib-2.0/gio/gsocketaddressenumerator.h
#usr/include/glib-2.0/gio/gsocketclient.h
#usr/include/glib-2.0/gio/gsocketconnectable.h
#usr/include/glib-2.0/gio/gsocketconnection.h
#usr/include/glib-2.0/gio/gsocketcontrolmessage.h
#usr/include/glib-2.0/gio/gsocketlistener.h
#usr/include/glib-2.0/gio/gsocketservice.h
#usr/include/glib-2.0/gio/gsrvtarget.h
#usr/include/glib-2.0/gio/gsubprocess.h
#usr/include/glib-2.0/gio/gsubprocesslauncher.h
#usr/include/glib-2.0/gio/gtask.h
#usr/include/glib-2.0/gio/gtcpconnection.h
#usr/include/glib-2.0/gio/gtcpwrapperconnection.h
#usr/include/glib-2.0/gio/gtestdbus.h
#usr/include/glib-2.0/gio/gthemedicon.h
#usr/include/glib-2.0/gio/gthreadedsocketservice.h
#usr/include/glib-2.0/gio/gtlsbackend.h
#usr/include/glib-2.0/gio/gtlscertificate.h
#usr/include/glib-2.0/gio/gtlsclientconnection.h
#usr/include/glib-2.0/gio/gtlsconnection.h
#usr/include/glib-2.0/gio/gtlsdatabase.h
#usr/include/glib-2.0/gio/gtlsfiledatabase.h
#usr/include/glib-2.0/gio/gtlsinteraction.h
#usr/include/glib-2.0/gio/gtlspassword.h
#usr/include/glib-2.0/gio/gtlsserverconnection.h
#usr/include/glib-2.0/gio/gunixconnection.h
#usr/include/glib-2.0/gio/gunixcredentialsmessage.h
#usr/include/glib-2.0/gio/gunixfdlist.h
#usr/include/glib-2.0/gio/gunixsocketaddress.h
#usr/include/glib-2.0/gio/gvfs.h
#usr/include/glib-2.0/gio/gvolume.h
#usr/include/glib-2.0/gio/gvolumemonitor.h
#usr/include/glib-2.0/gio/gzlibcompressor.h
#usr/include/glib-2.0/gio/gzlibdecompressor.h
#usr/include/glib-2.0/glib
#usr/include/glib-2.0/glib-object.h
#usr/include/glib-2.0/glib-unix.h
#usr/include/glib-2.0/glib.h
#usr/include/glib-2.0/glib/deprecated
#usr/include/glib-2.0/glib/deprecated/gallocator.h
#usr/include/glib-2.0/glib/deprecated/gcache.h
#usr/include/glib-2.0/glib/deprecated/gcompletion.h
#usr/include/glib-2.0/glib/deprecated/gmain.h
#usr/include/glib-2.0/glib/deprecated/grel.h
#usr/include/glib-2.0/glib/deprecated/gthread.h
#usr/include/glib-2.0/glib/galloca.h
#usr/include/glib-2.0/glib/garray.h
#usr/include/glib-2.0/glib/gasyncqueue.h
#usr/include/glib-2.0/glib/gatomic.h
#usr/include/glib-2.0/glib/gbacktrace.h
#usr/include/glib-2.0/glib/gbase64.h
#usr/include/glib-2.0/glib/gbitlock.h
#usr/include/glib-2.0/glib/gbookmarkfile.h
#usr/include/glib-2.0/glib/gbytes.h
#usr/include/glib-2.0/glib/gcharset.h
#usr/include/glib-2.0/glib/gchecksum.h
#usr/include/glib-2.0/glib/gconvert.h
#usr/include/glib-2.0/glib/gdataset.h
#usr/include/glib-2.0/glib/gdate.h
#usr/include/glib-2.0/glib/gdatetime.h
#usr/include/glib-2.0/glib/gdir.h
#usr/include/glib-2.0/glib/genviron.h
#usr/include/glib-2.0/glib/gerror.h
#usr/include/glib-2.0/glib/gfileutils.h
#usr/include/glib-2.0/glib/ggettext.h
#usr/include/glib-2.0/glib/ghash.h
#usr/include/glib-2.0/glib/ghmac.h
#usr/include/glib-2.0/glib/ghook.h
#usr/include/glib-2.0/glib/ghostutils.h
#usr/include/glib-2.0/glib/gi18n-lib.h
#usr/include/glib-2.0/glib/gi18n.h
#usr/include/glib-2.0/glib/giochannel.h
#usr/include/glib-2.0/glib/gkeyfile.h
#usr/include/glib-2.0/glib/glib-autocleanups.h
#usr/include/glib-2.0/glib/glib-typeof.h
#usr/include/glib-2.0/glib/glib-visibility.h
#usr/include/glib-2.0/glib/glist.h
#usr/include/glib-2.0/glib/gmacros.h
#usr/include/glib-2.0/glib/gmain.h
#usr/include/glib-2.0/glib/gmappedfile.h
#usr/include/glib-2.0/glib/gmarkup.h
#usr/include/glib-2.0/glib/gmem.h
#usr/include/glib-2.0/glib/gmessages.h
#usr/include/glib-2.0/glib/gnode.h
#usr/include/glib-2.0/glib/goption.h
#usr/include/glib-2.0/glib/gpathbuf.h
#usr/include/glib-2.0/glib/gpattern.h
#usr/include/glib-2.0/glib/gpoll.h
#usr/include/glib-2.0/glib/gprimes.h
#usr/include/glib-2.0/glib/gprintf.h
#usr/include/glib-2.0/glib/gqsort.h
#usr/include/glib-2.0/glib/gquark.h
#usr/include/glib-2.0/glib/gqueue.h
#usr/include/glib-2.0/glib/grand.h
#usr/include/glib-2.0/glib/grcbox.h
#usr/include/glib-2.0/glib/grefcount.h
#usr/include/glib-2.0/glib/grefstring.h
#usr/include/glib-2.0/glib/gregex.h
#usr/include/glib-2.0/glib/gscanner.h
#usr/include/glib-2.0/glib/gsequence.h
#usr/include/glib-2.0/glib/gshell.h
#usr/include/glib-2.0/glib/gslice.h
#usr/include/glib-2.0/glib/gslist.h
#usr/include/glib-2.0/glib/gspawn.h
#usr/include/glib-2.0/glib/gstdio.h
#usr/include/glib-2.0/glib/gstrfuncs.h
#usr/include/glib-2.0/glib/gstring.h
#usr/include/glib-2.0/glib/gstringchunk.h
#usr/include/glib-2.0/glib/gstrvbuilder.h
#usr/include/glib-2.0/glib/gtestutils.h
#usr/include/glib-2.0/glib/gthread.h
#usr/include/glib-2.0/glib/gthreadpool.h
#usr/include/glib-2.0/glib/gtimer.h
#usr/include/glib-2.0/glib/gtimezone.h
#usr/include/glib-2.0/glib/gtrashstack.h
#usr/include/glib-2.0/glib/gtree.h
#usr/include/glib-2.0/glib/gtypes.h
#usr/include/glib-2.0/glib/gunicode.h
#usr/include/glib-2.0/glib/guri.h
#usr/include/glib-2.0/glib/gutils.h
#usr/include/glib-2.0/glib/guuid.h
#usr/include/glib-2.0/glib/gvariant.h
#usr/include/glib-2.0/glib/gvarianttype.h
#usr/include/glib-2.0/glib/gversion.h
#usr/include/glib-2.0/glib/gversionmacros.h
#usr/include/glib-2.0/glib/gwin32.h
#usr/include/glib-2.0/gmodule
#usr/include/glib-2.0/gmodule.h
#usr/include/glib-2.0/gmodule/gmodule-visibility.h
#usr/include/glib-2.0/gobject
#usr/include/glib-2.0/gobject/gbinding.h
#usr/include/glib-2.0/gobject/gbindinggroup.h
#usr/include/glib-2.0/gobject/gboxed.h
#usr/include/glib-2.0/gobject/gclosure.h
#usr/include/glib-2.0/gobject/genums.h
#usr/include/glib-2.0/gobject/glib-enumtypes.h
#usr/include/glib-2.0/gobject/glib-types.h
#usr/include/glib-2.0/gobject/gmarshal.h
#usr/include/glib-2.0/gobject/gobject-autocleanups.h
#usr/include/glib-2.0/gobject/gobject-visibility.h
#usr/include/glib-2.0/gobject/gobject.h
#usr/include/glib-2.0/gobject/gobjectnotifyqueue.c
#usr/include/glib-2.0/gobject/gparam.h
#usr/include/glib-2.0/gobject/gparamspecs.h
#usr/include/glib-2.0/gobject/gsignal.h
#usr/include/glib-2.0/gobject/gsignalgroup.h
#usr/include/glib-2.0/gobject/gsourceclosure.h
#usr/include/glib-2.0/gobject/gtype.h
#usr/include/glib-2.0/gobject/gtypemodule.h
#usr/include/glib-2.0/gobject/gtypeplugin.h
#usr/include/glib-2.0/gobject/gvalue.h
#usr/include/glib-2.0/gobject/gvaluearray.h
#usr/include/glib-2.0/gobject/gvaluecollector.h
#usr/include/glib-2.0/gobject/gvaluetypes.h
#usr/lib/gio
#usr/lib/gio/modules
#usr/lib/glib-2.0
#usr/lib/glib-2.0/include
#usr/lib/glib-2.0/include/glibconfig.h
#usr/lib/libgio-2.0.so
usr/lib/libgio-2.0.so.0
usr/lib/libgio-2.0.so.0.7700.0
#usr/lib/libglib-2.0.so
usr/lib/libglib-2.0.so.0
usr/lib/libglib-2.0.so.0.7700.0
#usr/lib/libgmodule-2.0.so
usr/lib/libgmodule-2.0.so.0
usr/lib/libgmodule-2.0.so.0.7700.0
#usr/lib/libgobject-2.0.so
usr/lib/libgobject-2.0.so.0
usr/lib/libgobject-2.0.so.0.7700.0
#usr/lib/libgthread-2.0.so
usr/lib/libgthread-2.0.so.0
usr/lib/libgthread-2.0.so.0.7700.0
#usr/lib/pkgconfig/gio-2.0.pc
#usr/lib/pkgconfig/gio-unix-2.0.pc
#usr/lib/pkgconfig/glib-2.0.pc
#usr/lib/pkgconfig/gmodule-2.0.pc
#usr/lib/pkgconfig/gmodule-export-2.0.pc
#usr/lib/pkgconfig/gmodule-no-export-2.0.pc
#usr/lib/pkgconfig/gobject-2.0.pc
#usr/lib/pkgconfig/gthread-2.0.pc
#usr/libexec/gio-launch-desktop
#usr/share/aclocal/glib-2.0.m4
#usr/share/aclocal/glib-gettext.m4
#usr/share/aclocal/gsettings.m4
#usr/share/bash-completion/completions/gapplication
#usr/share/bash-completion/completions/gdbus
#usr/share/bash-completion/completions/gio
#usr/share/bash-completion/completions/gresource
#usr/share/bash-completion/completions/gsettings
#usr/share/gdb
#usr/share/gdb/auto-load
#usr/share/gdb/auto-load/usr
#usr/share/gdb/auto-load/usr/lib
#usr/share/gdb/auto-load/usr/lib/libglib-2.0.so.0.7700.0-gdb.py
#usr/share/gdb/auto-load/usr/lib/libgobject-2.0.so.0.7700.0-gdb.py
#usr/share/gettext/its
#usr/share/gettext/its/gschema.its
#usr/share/gettext/its/gschema.loc
#usr/share/glib-2.0
#usr/share/glib-2.0/codegen
#usr/share/glib-2.0/codegen/__init__.py
#usr/share/glib-2.0/codegen/codegen.py
#usr/share/glib-2.0/codegen/codegen_docbook.py
#usr/share/glib-2.0/codegen/codegen_main.py
#usr/share/glib-2.0/codegen/codegen_md.py
#usr/share/glib-2.0/codegen/codegen_rst.py
#usr/share/glib-2.0/codegen/config.py
#usr/share/glib-2.0/codegen/dbustypes.py
#usr/share/glib-2.0/codegen/parser.py
#usr/share/glib-2.0/codegen/utils.py
#usr/share/glib-2.0/gdb
#usr/share/glib-2.0/gdb/glib_gdb.py
#usr/share/glib-2.0/gdb/gobject_gdb.py
#usr/share/glib-2.0/gettext
#usr/share/glib-2.0/gettext/po
#usr/share/glib-2.0/gettext/po/Makefile.in.in
#usr/share/glib-2.0/schemas
#usr/share/glib-2.0/schemas/gschema.dtd
#usr/share/glib-2.0/valgrind
#usr/share/glib-2.0/valgrind/glib.supp
#usr/share/locale/ab
#usr/share/locale/ab/LC_MESSAGES
#usr/share/locale/ab/LC_MESSAGES/glib20.mo
#usr/share/locale/af/LC_MESSAGES/glib20.mo
#usr/share/locale/am
#usr/share/locale/am/LC_MESSAGES
#usr/share/locale/am/LC_MESSAGES/glib20.mo
#usr/share/locale/an
#usr/share/locale/an/LC_MESSAGES
#usr/share/locale/an/LC_MESSAGES/glib20.mo
#usr/share/locale/ar
#usr/share/locale/ar/LC_MESSAGES
#usr/share/locale/ar/LC_MESSAGES/glib20.mo
#usr/share/locale/as
#usr/share/locale/as/LC_MESSAGES
#usr/share/locale/as/LC_MESSAGES/glib20.mo
#usr/share/locale/ast/LC_MESSAGES/glib20.mo
#usr/share/locale/az
#usr/share/locale/az/LC_MESSAGES
#usr/share/locale/az/LC_MESSAGES/glib20.mo
#usr/share/locale/be/LC_MESSAGES/glib20.mo
#usr/share/locale/be@latin
#usr/share/locale/be@latin/LC_MESSAGES
#usr/share/locale/be@latin/LC_MESSAGES/glib20.mo
#usr/share/locale/bg/LC_MESSAGES/glib20.mo
#usr/share/locale/bn
#usr/share/locale/bn/LC_MESSAGES
#usr/share/locale/bn/LC_MESSAGES/glib20.mo
#usr/share/locale/bn_IN
#usr/share/locale/bn_IN/LC_MESSAGES
#usr/share/locale/bn_IN/LC_MESSAGES/glib20.mo
#usr/share/locale/bs
#usr/share/locale/bs/LC_MESSAGES
#usr/share/locale/bs/LC_MESSAGES/glib20.mo
#usr/share/locale/ca/LC_MESSAGES/glib20.mo
#usr/share/locale/ca@valencia
#usr/share/locale/ca@valencia/LC_MESSAGES
#usr/share/locale/ca@valencia/LC_MESSAGES/glib20.mo
#usr/share/locale/cs/LC_MESSAGES/glib20.mo
#usr/share/locale/cy
#usr/share/locale/cy/LC_MESSAGES
#usr/share/locale/cy/LC_MESSAGES/glib20.mo
#usr/share/locale/da/LC_MESSAGES/glib20.mo
#usr/share/locale/de/LC_MESSAGES/glib20.mo
#usr/share/locale/dz
#usr/share/locale/dz/LC_MESSAGES
#usr/share/locale/dz/LC_MESSAGES/glib20.mo
#usr/share/locale/el/LC_MESSAGES/glib20.mo
#usr/share/locale/en@shaw
#usr/share/locale/en@shaw/LC_MESSAGES
#usr/share/locale/en@shaw/LC_MESSAGES/glib20.mo
#usr/share/locale/en_CA
#usr/share/locale/en_CA/LC_MESSAGES
#usr/share/locale/en_CA/LC_MESSAGES/glib20.mo
#usr/share/locale/en_GB/LC_MESSAGES/glib20.mo
#usr/share/locale/eo/LC_MESSAGES/glib20.mo
#usr/share/locale/es/LC_MESSAGES/glib20.mo
#usr/share/locale/et/LC_MESSAGES/glib20.mo
#usr/share/locale/eu/LC_MESSAGES/glib20.mo
#usr/share/locale/fa
#usr/share/locale/fa/LC_MESSAGES
#usr/share/locale/fa/LC_MESSAGES/glib20.mo
#usr/share/locale/fi/LC_MESSAGES/glib20.mo
#usr/share/locale/fr/LC_MESSAGES/glib20.mo
#usr/share/locale/fur
#usr/share/locale/fur/LC_MESSAGES
#usr/share/locale/fur/LC_MESSAGES/glib20.mo
#usr/share/locale/ga/LC_MESSAGES/glib20.mo
#usr/share/locale/gd
#usr/share/locale/gd/LC_MESSAGES
#usr/share/locale/gd/LC_MESSAGES/glib20.mo
#usr/share/locale/gl/LC_MESSAGES/glib20.mo
#usr/share/locale/gu
#usr/share/locale/gu/LC_MESSAGES
#usr/share/locale/gu/LC_MESSAGES/glib20.mo
#usr/share/locale/he
#usr/share/locale/he/LC_MESSAGES
#usr/share/locale/he/LC_MESSAGES/glib20.mo
#usr/share/locale/hi
#usr/share/locale/hi/LC_MESSAGES
#usr/share/locale/hi/LC_MESSAGES/glib20.mo
#usr/share/locale/hr/LC_MESSAGES/glib20.mo
#usr/share/locale/hu/LC_MESSAGES/glib20.mo
#usr/share/locale/hy
#usr/share/locale/hy/LC_MESSAGES
#usr/share/locale/hy/LC_MESSAGES/glib20.mo
#usr/share/locale/id/LC_MESSAGES/glib20.mo
#usr/share/locale/ie
#usr/share/locale/ie/LC_MESSAGES
#usr/share/locale/ie/LC_MESSAGES/glib20.mo
#usr/share/locale/is
#usr/share/locale/is/LC_MESSAGES
#usr/share/locale/is/LC_MESSAGES/glib20.mo
#usr/share/locale/it/LC_MESSAGES/glib20.mo
#usr/share/locale/ja/LC_MESSAGES/glib20.mo
#usr/share/locale/ka/LC_MESSAGES/glib20.mo
#usr/share/locale/kk/LC_MESSAGES/glib20.mo
#usr/share/locale/kn
#usr/share/locale/kn/LC_MESSAGES
#usr/share/locale/kn/LC_MESSAGES/glib20.mo
#usr/share/locale/ko/LC_MESSAGES/glib20.mo
#usr/share/locale/ku
#usr/share/locale/ku/LC_MESSAGES
#usr/share/locale/ku/LC_MESSAGES/glib20.mo
#usr/share/locale/lt/LC_MESSAGES/glib20.mo
#usr/share/locale/lv/LC_MESSAGES/glib20.mo
#usr/share/locale/mai
#usr/share/locale/mai/LC_MESSAGES
#usr/share/locale/mai/LC_MESSAGES/glib20.mo
#usr/share/locale/mg
#usr/share/locale/mg/LC_MESSAGES
#usr/share/locale/mg/LC_MESSAGES/glib20.mo
#usr/share/locale/mk
#usr/share/locale/mk/LC_MESSAGES
#usr/share/locale/mk/LC_MESSAGES/glib20.mo
#usr/share/locale/ml
#usr/share/locale/ml/LC_MESSAGES
#usr/share/locale/ml/LC_MESSAGES/glib20.mo
#usr/share/locale/mn
#usr/share/locale/mn/LC_MESSAGES
#usr/share/locale/mn/LC_MESSAGES/glib20.mo
#usr/share/locale/mr
#usr/share/locale/mr/LC_MESSAGES
#usr/share/locale/mr/LC_MESSAGES/glib20.mo
#usr/share/locale/ms/LC_MESSAGES/glib20.mo
#usr/share/locale/nb/LC_MESSAGES/glib20.mo
#usr/share/locale/nds
#usr/share/locale/nds/LC_MESSAGES
#usr/share/locale/nds/LC_MESSAGES/glib20.mo
#usr/share/locale/ne
#usr/share/locale/ne/LC_MESSAGES
#usr/share/locale/ne/LC_MESSAGES/glib20.mo
#usr/share/locale/nl/LC_MESSAGES/glib20.mo
#usr/share/locale/nn
#usr/share/locale/nn/LC_MESSAGES
#usr/share/locale/nn/LC_MESSAGES/glib20.mo
#usr/share/locale/oc
#usr/share/locale/oc/LC_MESSAGES
#usr/share/locale/oc/LC_MESSAGES/glib20.mo
#usr/share/locale/or
#usr/share/locale/or/LC_MESSAGES
#usr/share/locale/or/LC_MESSAGES/glib20.mo
#usr/share/locale/pa
#usr/share/locale/pa/LC_MESSAGES
#usr/share/locale/pa/LC_MESSAGES/glib20.mo
#usr/share/locale/pl/LC_MESSAGES/glib20.mo
#usr/share/locale/ps
#usr/share/locale/ps/LC_MESSAGES
#usr/share/locale/ps/LC_MESSAGES/glib20.mo
#usr/share/locale/pt/LC_MESSAGES/glib20.mo
#usr/share/locale/pt_BR/LC_MESSAGES/glib20.mo
#usr/share/locale/ro/LC_MESSAGES/glib20.mo
#usr/share/locale/ru/LC_MESSAGES/glib20.mo
#usr/share/locale/rw/LC_MESSAGES/glib20.mo
#usr/share/locale/si
#usr/share/locale/si/LC_MESSAGES
#usr/share/locale/si/LC_MESSAGES/glib20.mo
#usr/share/locale/sk/LC_MESSAGES/glib20.mo
#usr/share/locale/sl/LC_MESSAGES/glib20.mo
#usr/share/locale/sq/LC_MESSAGES/glib20.mo
#usr/share/locale/sr/LC_MESSAGES/glib20.mo
#usr/share/locale/sr@ije
#usr/share/locale/sr@ije/LC_MESSAGES
#usr/share/locale/sr@ije/LC_MESSAGES/glib20.mo
#usr/share/locale/sr@latin
#usr/share/locale/sr@latin/LC_MESSAGES
#usr/share/locale/sr@latin/LC_MESSAGES/glib20.mo
#usr/share/locale/sv/LC_MESSAGES/glib20.mo
#usr/share/locale/ta/LC_MESSAGES/glib20.mo
#usr/share/locale/te
#usr/share/locale/te/LC_MESSAGES
#usr/share/locale/te/LC_MESSAGES/glib20.mo
#usr/share/locale/tg
#usr/share/locale/tg/LC_MESSAGES
#usr/share/locale/tg/LC_MESSAGES/glib20.mo
#usr/share/locale/th/LC_MESSAGES/glib20.mo
#usr/share/locale/tl
#usr/share/locale/tl/LC_MESSAGES
#usr/share/locale/tl/LC_MESSAGES/glib20.mo
#usr/share/locale/tr/LC_MESSAGES/glib20.mo
#usr/share/locale/tt
#usr/share/locale/tt/LC_MESSAGES
#usr/share/locale/tt/LC_MESSAGES/glib20.mo
#usr/share/locale/ug
#usr/share/locale/ug/LC_MESSAGES
#usr/share/locale/ug/LC_MESSAGES/glib20.mo
#usr/share/locale/uk/LC_MESSAGES/glib20.mo
#usr/share/locale/vi/LC_MESSAGES/glib20.mo
#usr/share/locale/wa
#usr/share/locale/wa/LC_MESSAGES
#usr/share/locale/wa/LC_MESSAGES/glib20.mo
#usr/share/locale/xh
#usr/share/locale/xh/LC_MESSAGES
#usr/share/locale/xh/LC_MESSAGES/glib20.mo
#usr/share/locale/yi
#usr/share/locale/yi/LC_MESSAGES
#usr/share/locale/yi/LC_MESSAGES/glib20.mo
#usr/share/locale/zh_CN/LC_MESSAGES/glib20.mo
#usr/share/locale/zh_HK
#usr/share/locale/zh_HK/LC_MESSAGES
#usr/share/locale/zh_HK/LC_MESSAGES/glib20.mo
#usr/share/locale/zh_TW/LC_MESSAGES/glib20.mo


set default="0"

function load_video {
  insmod efi_gop
  insmod efi_uga
  insmod video_bochs
  insmod video_cirrus
  insmod all_video
}

load_video
set gfxpayload=keep
insmod gzio
insmod part_gpt
insmod ext2

set timeout=60

menuentry 'Install NAME VERSION ARCH' --class ipfire --class gnu-linux --class gnu --class os {
	linux /boot/isolinux/vmlinuz
	initrd /boot/isolinux/instroot
}

submenu 'Other Installation Options -->' {
	menuentry 'Install NAME VERSION (Text Mode)' --class ipfire --class gnu-linux --class gnu --class os {
		linux /boot/isolinux/vmlinuz novga
		initrd /boot/isolinux/instroot
	}

	menuentry 'Unattended installation' --class ipfire --class gnu-linux --class gnu --class os {
		linux /boot/isolinux/vmlinuz installer.unattended
		initrd /boot/isolinux/instroot
	}
}

submenu 'Tools -->' {
	menuentry 'memtest86+' {
		linux /boot/isolinux/memtest
	}
}

    $subfirewall->{'10.firewall'} = {
				'caption' => $Lang::tr{'firewall rules'},
				'uri' => '/cgi-bin/firewall.cgi',
				'title' => "$Lang::tr{'firewall rules'}",
				'enabled' => 1,
				};
	$subfirewall->{'20.fwhost'} = {
				'caption' => $Lang::tr{'fwhost menu'},
				'uri' => '/cgi-bin/fwhosts.cgi',
				'title' => "$Lang::tr{'fwhost menu'}",
				'enabled' => 1,
				};
    $subfirewall->{'30.optionsfw'} = {
				'caption' => $Lang::tr{'options fw'},
				'uri' => '/cgi-bin/optionsfw.cgi',
				'title' => "$Lang::tr{'options fw'}",
				'enabled' => 1,
				};
     $subfirewall->{'40.ids'} = {'caption' => $Lang::tr{'intrusion detection'},
                                'uri' => '/cgi-bin/ids.cgi',
                                'title' => "$Lang::tr{'intrusion detection system'}",
				'enabled' => 1,
                                };
	$subfirewall->{'50.ipblocklist'} = {'caption' => $Lang::tr{'ipblocklist'},
				'uri' => '/cgi-bin/ipblocklist.cgi',
				'title' => "$Lang::tr{'ipblocklist'}",
				'enabled' => 1,
				};
    $subfirewall->{'70.wireless'} = {
				'caption' => $Lang::tr{'blue access'},
				'uri' => '/cgi-bin/wireless.cgi',
				'title' => "$Lang::tr{'blue access'}",
				'enabled' => 1,
				 };			 
    $subfirewall->{'90.iptables'} = {
				'caption' => $Lang::tr{'ipts'},
				'uri' => '/cgi-bin/iptables.cgi',
				'title' => "$Lang::tr{'ipts'}",
				'enabled' => 1,
				};

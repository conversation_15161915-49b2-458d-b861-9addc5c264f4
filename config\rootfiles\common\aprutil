usr/bin/apu-1-config
#usr/include/apr-1/apr_anylock.h
#usr/include/apr-1/apr_base64.h
#usr/include/apr-1/apr_buckets.h
#usr/include/apr-1/apr_crypto.h
#usr/include/apr-1/apr_date.h
#usr/include/apr-1/apr_dbd.h
#usr/include/apr-1/apr_dbm.h
#usr/include/apr-1/apr_hooks.h
#usr/include/apr-1/apr_ldap.h
#usr/include/apr-1/apr_ldap_init.h
#usr/include/apr-1/apr_ldap_option.h
#usr/include/apr-1/apr_ldap_rebind.h
#usr/include/apr-1/apr_ldap_url.h
#usr/include/apr-1/apr_md4.h
#usr/include/apr-1/apr_md5.h
#usr/include/apr-1/apr_memcache.h
#usr/include/apr-1/apr_optional.h
#usr/include/apr-1/apr_optional_hooks.h
#usr/include/apr-1/apr_queue.h
#usr/include/apr-1/apr_redis.h
#usr/include/apr-1/apr_reslist.h
#usr/include/apr-1/apr_rmm.h
#usr/include/apr-1/apr_sdbm.h
#usr/include/apr-1/apr_sha1.h
#usr/include/apr-1/apr_siphash.h
#usr/include/apr-1/apr_strmatch.h
#usr/include/apr-1/apr_thread_pool.h
#usr/include/apr-1/apr_uri.h
#usr/include/apr-1/apr_uuid.h
#usr/include/apr-1/apr_xlate.h
#usr/include/apr-1/apr_xml.h
#usr/include/apr-1/apu.h
#usr/include/apr-1/apu_errno.h
#usr/include/apr-1/apu_version.h
#usr/include/apr-1/apu_want.h
#usr/lib/apr-util-1
usr/lib/apr-util-1/apr_crypto_openssl-1.so
#usr/lib/apr-util-1/apr_crypto_openssl.la
usr/lib/apr-util-1/apr_crypto_openssl.so
usr/lib/apr-util-1/apr_dbd_sqlite3-1.so
#usr/lib/apr-util-1/apr_dbd_sqlite3.la
usr/lib/apr-util-1/apr_dbd_sqlite3.so
usr/lib/apr-util-1/apr_dbm_gdbm-1.so
#usr/lib/apr-util-1/apr_dbm_gdbm.la
usr/lib/apr-util-1/apr_dbm_gdbm.so
#usr/lib/aprutil.exp
#usr/lib/libaprutil-1.la
#usr/lib/libaprutil-1.so
usr/lib/libaprutil-1.so.0
usr/lib/libaprutil-1.so.0.6.3
#usr/lib/pkgconfig/apr-util-1.pc

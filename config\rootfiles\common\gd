#usr/bin/annotate
#usr/bin/bdftogd
#usr/bin/gd2copypal
#usr/bin/gd2togif
#usr/bin/gd2topng
#usr/bin/gdcmpgif
#usr/bin/gdparttopng
#usr/bin/gdtopng
#usr/bin/giftogd2
#usr/bin/pngtogd
#usr/bin/pngtogd2
#usr/bin/webpng
#usr/include/gd.h
#usr/include/gd_color_map.h
#usr/include/gd_errors.h
#usr/include/gd_io.h
#usr/include/gdcache.h
#usr/include/gdfontg.h
#usr/include/gdfontl.h
#usr/include/gdfontmb.h
#usr/include/gdfonts.h
#usr/include/gdfontt.h
#usr/include/gdfx.h
#usr/include/gdpp.h
#usr/lib/libgd.la
#usr/lib/libgd.so
usr/lib/libgd.so.3
usr/lib/libgd.so.3.0.11
#usr/lib/pkgconfig/gdlib.pc

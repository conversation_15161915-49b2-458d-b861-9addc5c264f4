HOME				= .
oid_section			= new_oids

[ new_oids ]

[ ca ]
default_ca			= openvpn

[ openvpn ]
dir				= /var/ipfire/ovpn
certs				= $dir/certs
crl_dir				= $dir/crl
database			= $dir/certs/index.txt
new_certs_dir			= $dir/certs
certificate			= $dir/ca/cacert.pem
serial				= $dir/certs/serial
crl				= $dir/crl.pem
private_key			= $dir/ca/cakey.pem
x509_extensions			= usr_cert
default_days			= 999999
default_crl_days		= 30
default_md			= sha256
preserve			= no
policy				= policy_match
email_in_dn			= no

[ policy_match ]
countryName			= optional
stateOrProvinceName		= optional
organizationName		= optional
organizationalUnitName		= optional
commonName			= supplied
emailAddress			= optional

[ req ]
default_bits			= 2048
default_keyfile 		= privkey.pem
distinguished_name		= req_distinguished_name
attributes			= req_attributes
x509_extensions			= v3_ca
string_mask 			= nombstr

[ req_distinguished_name ]
countryName			= Country Name (2 letter code)
countryName_default		= GB
countryName_min			= 2
countryName_max			= 2

stateOrProvinceName		= State or Province Name (full name)
stateOrProvinceName_default	= 

localityName			= Locality Name (eg, city)
#localityName_default		= 

0.organizationName		= Organization Name (eg, company)
0.organizationName_default	= My Company Ltd

organizationalUnitName		= Organizational Unit Name (eg, section)
#organizationalUnitName_default	=

commonName			= Common Name (eg, your name or your server\'s hostname)
commonName_max			= 64

emailAddress			= Email Address
emailAddress_max		= 40

[ req_attributes ]
challengePassword		= A challenge password
challengePassword_min		= 4
challengePassword_max		= 20
unstructuredName		= An optional company name

[ usr_cert ]
basicConstraints		= CA:FALSE
nsComment			= "OpenSSL Generated Certificate"
subjectKeyIdentifier		= hash
authorityKeyIdentifier		= keyid,issuer:always
extendedKeyUsage               = clientAuth
keyUsage                       = digitalSignature

[ server ]

# JY ADDED -- Make a cert with nsCertType set to "server"
basicConstraints		= CA:FALSE
nsCertType			= server
nsComment			= "OpenSSL Generated Server Certificate"
subjectKeyIdentifier		= hash
authorityKeyIdentifier		= keyid,issuer:always 
extendedKeyUsage               = serverAuth
keyUsage                       = digitalSignature, keyEncipherment

[ v3_req ]
basicConstraints 		= CA:FALSE
keyUsage 			= nonRepudiation, digitalSignature, keyEncipherment

[ v3_ca ]
subjectKeyIdentifier		= hash
authorityKeyIdentifier		= keyid:always,issuer:always
basicConstraints 		= CA:true

[ crl_ext ]
authorityKeyIdentifier		= keyid:always,issuer:always

[ engine ]
default 			= openssl

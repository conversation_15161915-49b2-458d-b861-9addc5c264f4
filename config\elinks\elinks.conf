## ELinks 0.11.4rc0 configuration file

## This is ELinks configuration file. You can edit it manually,
## if you wish so; this file is edited by ELink<PERSON> when you save
## options through UI, however only option values will be altered
## and missing options will be added at the end of file; if option
## is not written in this file, but in some file included from it,
## it is NOT counted as missing. Note that all your formatting,
## own comments and so on will be kept as-is.
##
## Obviously, if you don't like what ELinks is going to do with
## this file, you can change it by altering the config.saving_style
## option. Come on, aren't we friendly guys after all?



##############################
# Automatically saved options
#

  ## config.saving_style_w [0|1]
  #  This is internal option used when displaying a warning about
  #  obsolete config.saving_style. You shouldn't touch it.
  set config.saving_style_w = 1

    ## terminal.linux.utf_8_io [0|1]
    set terminal.linux.utf_8_io = 1
  ## ui.language <language>
  #  Language of user interface. 'System' means that the language will
  #  be extracted from the environment dynamically.
  set ui.language = "System"




##############################
# Automatically saved options
#

    ## ui.sessions.homepage <str>
    #  The URI to load either at startup time when no URI was given
    #  on the command line or when requested by the goto-url-home action.
    #  Set to "" if the environment variable WWW_HOME should be used
    #  as homepage URI instead.
    set ui.sessions.homepage = "https://localhost:444"


#
# crontab for ipfire
#
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=
HOME=/

# Do all jobs in this directories
*/1 * * * *	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.minutely
*/5 * * * *	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.cyclic
01 * * * *	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.hourly
&nice(10),bootrun 25 1 * * *	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.daily
&nice(10),bootrun 47 2 * * 1	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.weekly
&nice(10),bootrun 52 3 1 * *	test -x /usr/local/bin/run-parts && /usr/local/bin/run-parts /etc/fcron.monthly

# Log rotation
01 * * * *	/usr/sbin/logrotate /etc/logrotate.conf

# Set time
*/5 * * * *	/usr/local/bin/timecheck > /dev/null 2>&1

# Make some nice graphs
*/5 * * * * 	/usr/local/bin/makegraphs >/dev/null

# Backup ramdisks if necessary
%nightly,random * 23-4	/etc/init.d/collectd backup &>/dev/null
%nightly,random * 23-4	/etc/init.d/vnstat backup &>/dev/null

# Update dynamic DNS records every five minutes.
*/5 * * * *	[ -f "/var/ipfire/red/active" ] && /usr/bin/ddns update-all

# Logwatch
05 0 * * *	/usr/local/bin/logwatch > /var/log/logwatch/`date -I -d yesterday`; \
		LOGWATCH_KEEP=$(sed -ne 's/^LOGWATCH_KEEP=\([0-9]\+\)$/\1/p' /var/ipfire/logging/settings); \
		find /var/log/logwatch/ -ctime +${LOGWATCH_KEEP=56} -exec rm -f '{}' ';'

# hddshutdown
2,32 * * * * 	/usr/local/bin/hddshutdown >/dev/null

# connection-scheduler
*/5 * * * *	/usr/local/bin/connscheduler timer > /dev/null

# URL Filter && Update Accelerator
%nightly * 3-5   /var/ipfire/urlfilter/autoupdate/cron.daily
%weekly  * 3-5   /var/ipfire/urlfilter/autoupdate/cron.weekly
%monthly * 3-5 * /var/ipfire/urlfilter/autoupdate/cron.monthly
%nightly * 3-5   /var/ipfire/updatexlrator/autocheck/cron.daily
%weekly  * 3-5   /var/ipfire/updatexlrator/autocheck/cron.weekly
%monthly * 3-5 * /var/ipfire/updatexlrator/autocheck/cron.monthly

# fireinfo
%nightly,random * 23-4	/usr/bin/sendprofile >/dev/null 2>&1

# pakfire
%nightly,random * 23-4	/usr/local/bin/pakfire update >/dev/null 2>&1

# Re-read firewall rules every Sunday in March, October and November to take care of daylight saving time
00 3 * 3 0          /usr/local/bin/timezone-transition /usr/local/bin/firewallctrl
00 2 * 10-11 0      /usr/local/bin/timezone-transition /usr/local/bin/firewallctrl

# Update location database
%hourly,random * [ -f "/var/ipfire/red/active" ] && /usr/local/bin/update-location-database >/dev/null 2>&1

# Perform a surciata rules update every 12 hours.
@ 12h [ -f "/var/ipfire/red/active" ] && /usr/local/bin/update-ids-ruleset >/dev/null 2>&1

# Update Lists for IP-based blocking every 15 minutes.
@ 15 [ -f "/var/ipfire/red/active" ] && /usr/local/bin/update-ipblocklists >/dev/null 2>&1

# Retry sending spooled mails regularly
%hourly * /usr/sbin/dma -q

# Cleanup captive clients
%hourly * /usr/bin/captive-cleanup

# Reload captive firewall rules
%nightly * 23-1   /usr/local/bin/captivectrl >/dev/null

# Cleanup the mail spool directory
%weekly * * /usr/sbin/dma-cleanup-spool

# Cleanup the collectd RRD (graphs)
%weekly * * /bin/find /var/log/rrd -mtime +365 -type f -name '*.rrd' -delete -o -type d -empty -delete

# Update DNS trust anchor
%daily,random * * @runas(nobody) /usr/sbin/unbound-anchor -a /var/lib/unbound/root.key -c /etc/unbound/icannbundle.pem

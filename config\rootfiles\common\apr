usr/bin/apr-1-config
#usr/include/apr-1
#usr/include/apr-1/apr.h
#usr/include/apr-1/apr_allocator.h
#usr/include/apr-1/apr_atomic.h
#usr/include/apr-1/apr_cstr.h
#usr/include/apr-1/apr_dso.h
#usr/include/apr-1/apr_encode.h
#usr/include/apr-1/apr_env.h
#usr/include/apr-1/apr_errno.h
#usr/include/apr-1/apr_escape.h
#usr/include/apr-1/apr_file_info.h
#usr/include/apr-1/apr_file_io.h
#usr/include/apr-1/apr_fnmatch.h
#usr/include/apr-1/apr_general.h
#usr/include/apr-1/apr_getopt.h
#usr/include/apr-1/apr_global_mutex.h
#usr/include/apr-1/apr_hash.h
#usr/include/apr-1/apr_inherit.h
#usr/include/apr-1/apr_lib.h
#usr/include/apr-1/apr_mmap.h
#usr/include/apr-1/apr_network_io.h
#usr/include/apr-1/apr_perms_set.h
#usr/include/apr-1/apr_poll.h
#usr/include/apr-1/apr_pools.h
#usr/include/apr-1/apr_portable.h
#usr/include/apr-1/apr_proc_mutex.h
#usr/include/apr-1/apr_random.h
#usr/include/apr-1/apr_ring.h
#usr/include/apr-1/apr_shm.h
#usr/include/apr-1/apr_signal.h
#usr/include/apr-1/apr_skiplist.h
#usr/include/apr-1/apr_strings.h
#usr/include/apr-1/apr_support.h
#usr/include/apr-1/apr_tables.h
#usr/include/apr-1/apr_thread_cond.h
#usr/include/apr-1/apr_thread_mutex.h
#usr/include/apr-1/apr_thread_proc.h
#usr/include/apr-1/apr_thread_rwlock.h
#usr/include/apr-1/apr_time.h
#usr/include/apr-1/apr_user.h
#usr/include/apr-1/apr_version.h
#usr/include/apr-1/apr_want.h
#usr/lib/apr.exp
#usr/lib/libapr-1.la
#usr/lib/libapr-1.so
usr/lib/libapr-1.so.0
usr/lib/libapr-1.so.0.7.4
#usr/lib/pkgconfig/apr-1.pc
#usr/share/apr-1
#usr/share/apr-1/build
#usr/share/apr-1/build/apr_rules.mk
#usr/share/apr-1/build/libtool
#usr/share/apr-1/build/make_exports.awk
#usr/share/apr-1/build/make_var_export.awk
#usr/share/apr-1/build/mkdir.sh

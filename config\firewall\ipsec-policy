#!/bin/bash
###############################################################################
#                                                                             #
# IPFire.org - A linux based firewall                                         #
# Copyright (C) 2015 IPFire Team                                              #
#                                                                             #
# This program is free software: you can redistribute it and/or modify        #
# it under the terms of the GNU General Public License as published by        #
# the Free Software Foundation, either version 3 of the License, or           #
# (at your option) any later version.                                         #
#                                                                             #
# This program is distributed in the hope that it will be useful,             #
# but WITHOUT ANY WARRANTY; without even the implied warranty of              #
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the               #
# GNU General Public License for more details.                                #
#                                                                             #
# You should have received a copy of the GNU General Public License           #
# along with this program.  If not, see <http://www.gnu.org/licenses/>.       #
#                                                                             #
###############################################################################

VPN_CONFIG="/var/ipfire/vpn/config"

eval $(/usr/local/bin/readhash /var/ipfire/vpn/settings)

VARS=(
	id status name lefthost type ctype psk local local_id leftsubnets
	remote_id remote rightsubnets x3 x4 x5 x6 x7 x8 x9 x10 x11 x12
	x13 x14 x15 x16 x17 x18 x19 proto x20 x21 x22
	route x23 mode interface_mode interface_address interface_mtu rest
)

block_subnet() {
	local subnet="${1}"
	local action="${2}"

	# Nothing to be done if no action is requested
	if [ "${action}" = "none" ]; then
		return 0
	fi

	# Don't block a wildcard subnet
	if [ "${subnet}" = "0.0.0.0/0" ] || [ "${subnet}" = "0.0.0.0/0.0.0.0" ]; then
		return 0
	fi

	case "${action}" in
		reject)
			iptables -A IPSECBLOCK -d "${subnet}" -j REJECT --reject-with icmp-net-unreachable
			;;
		drop)
			iptables -A IPSECBLOCK -d "${subnet}" -j DROP
			;;
		*)
			return 1
			;;
	esac

	return 0
}

install_policy() {
	# Flush existing rules
	iptables -F IPSECINPUT
	iptables -F IPSECOUTPUT
	iptables -F IPSECBLOCK

	# We are done when IPsec is not enabled
	[ "${ENABLED}" = "on" ] || exit 0

	# IKE
	iptables -A IPSECINPUT  -p udp --dport 500 -j ACCEPT
	iptables -A IPSECOUTPUT -p udp --dport 500 -j ACCEPT

	# IKE NAT
	iptables -A IPSECINPUT  -p udp --dport 4500 -j ACCEPT
	iptables -A IPSECOUTPUT -p udp --dport 4500 -j ACCEPT

	# Register local variables
	local "${VARS[@]}"
	local action

	while IFS="," read -r "${VARS[@]}"; do
		# Check if the connection is enabled
		[ "${status}" = "on" ] || continue

		# Check if this a net-to-net connection
		[ "${type}" = "net" ] || continue

		# Default local to 0.0.0.0/0
		if [ "${local}" = "" -o "${local}" = "off" ]; then
			local="0.0.0.0/0"
		fi

		# Install permissions for GRE traffic
		case "${interface_mode}" in
			gre)
				if [ -n "${remote}" ]; then
					iptables -A IPSECINPUT -p gre \
						-s "${remote}" -d "${local}" -j ACCEPT

					iptables -A IPSECOUTPUT -p gre \
						-s "${local}" -d "${remote}" -j ACCEPT
				fi
				;;
		esac

		# Install firewall rules only for interfaces without interface
		[ -n "${interface_mode}" ] && continue

		# Split multiple subnets
		rightsubnets="${rightsubnets//\|/ }"

		case "${route}" in
			route)
				action="none"
				;;
			*)
				action="reject"
				;;
		esac

		local rightsubnet
		for rightsubnet in ${rightsubnets}; do
			block_subnet "${rightsubnet}" "${action}"
		done
	done < "${VPN_CONFIG}"
}

install_policy || exit $?

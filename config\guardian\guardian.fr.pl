%tr = ( 
%tr,

'guardian' => 'Gardien',
'guardian block a host' => '<PERSON>lo<PERSON> l\'hôte ',
'guardian block httpd brute-force' => 'Détection force brute httpd',
'guardian block owncloud brute-force' => 'Détection force brute Owncloud',
'guardian block ssh brute-force' => 'Détection force brute SSH',
'guardian blockcount' => 'Seuil de déclenchement (Snort) ',
'guardian blocked hosts' => 'Hôtes bloqués actuellement',
'guardian blocking of this address is not allowed' => 'Le blocage de l\'adresse donnée n\'est pas autorisé.',
'guardian blocktime' => 'Temps de blocage (secondes) ',
'guardian common settings' => 'Paramètres communs',
'guardian configuration' => 'Configuration du gardien',
'guardian daemon' => 'Statut :',
'guardian empty input' => 'Saisie vide : Veuillez saisir une adresse d\'hôte ou un sous-réseau valide.',
'guardian enabled' => 'Activer le service ',
'guardian firewallaction' => 'Action du pare-feu ',
'guardian ignored hosts' => 'Hôtes ignorés',
'guardian invalid address or subnet' => 'Adresse hôte ou sous-réseau invalide.',
'guardian invalid blockcount' => 'Nombre de blocages non valide : Veuillez proposer un nombre plus grand que zéro.',
'guardian invalid blocktime' => 'Temps de blocage invalide : Veuillez proposer un nombre plus grand que zéro.',
'guardian invalid logfile' => 'Le chemin fourni pour le fichier de rapports n\'est pas valide.',
'guardian logfacility' => 'Type de rapport ',
'guardian logfile' => 'Fichier de rapport',
'guardian loglevel' => 'Niveau de rapport ',
'guardian loglevel_off' => 'Sans',
'guardian loglevel_info' => 'Info',
'guardian loglevel_debug' => 'Débug',
'guardian logtarget_syslog' => 'Rapport système',
'guardian logtarget_file' => 'Fichier',
'guardian logtarget_console' => 'Console',
'guardian no entries' => 'Aucune entrée pour le moment.',
'guardian not running no hosts can be blocked' => 'Le gardien n\'est pas en cours d\'exécution. Aucun hôte ne sera bloqué.',
'guardian priolevel_high' => '1 - Haut',
'guardian priolevel_medium' => '2 - Moyen',
'guardian priolevel_low' => '3 - Bas',
'guardian priolevel_very_low' => '4 - Très bas',
'guardian service' => 'Service Gardien',

);

#EOF

#usr/bin/gcore
#usr/bin/gdb
#usr/bin/gdb-add-index
#usr/bin/gdbserver
#usr/bin/run
#usr/include/gdb
#usr/include/gdb/jit-reader.h
#usr/include/sim
#usr/include/sim/callback.h
#usr/include/sim/sim.h
#usr/lib/libaarch64-unknown-linux-gnu-sim.a
#usr/lib/libinproctrace.so
#usr/share/gdb
#usr/share/gdb/python
#usr/share/gdb/python/gdb
#usr/share/gdb/python/gdb/FrameDecorator.py
#usr/share/gdb/python/gdb/FrameIterator.py
#usr/share/gdb/python/gdb/__init__.py
#usr/share/gdb/python/gdb/command
#usr/share/gdb/python/gdb/command/__init__.py
#usr/share/gdb/python/gdb/command/explore.py
#usr/share/gdb/python/gdb/command/frame_filters.py
#usr/share/gdb/python/gdb/command/pretty_printers.py
#usr/share/gdb/python/gdb/command/prompt.py
#usr/share/gdb/python/gdb/command/type_printers.py
#usr/share/gdb/python/gdb/command/unwinders.py
#usr/share/gdb/python/gdb/command/xmethods.py
#usr/share/gdb/python/gdb/frames.py
#usr/share/gdb/python/gdb/function
#usr/share/gdb/python/gdb/function/__init__.py
#usr/share/gdb/python/gdb/function/as_string.py
#usr/share/gdb/python/gdb/function/caller_is.py
#usr/share/gdb/python/gdb/function/strfns.py
#usr/share/gdb/python/gdb/printer
#usr/share/gdb/python/gdb/printer/__init__.py
#usr/share/gdb/python/gdb/printer/bound_registers.py
#usr/share/gdb/python/gdb/printing.py
#usr/share/gdb/python/gdb/prompt.py
#usr/share/gdb/python/gdb/types.py
#usr/share/gdb/python/gdb/unwinder.py
#usr/share/gdb/python/gdb/xmethod.py
#usr/share/gdb/syscalls
#usr/share/gdb/syscalls/aarch64-linux.xml
#usr/share/gdb/syscalls/amd64-linux.xml
#usr/share/gdb/syscalls/arm-linux.xml
#usr/share/gdb/syscalls/freebsd.xml
#usr/share/gdb/syscalls/gdb-syscalls.dtd
#usr/share/gdb/syscalls/i386-linux.xml
#usr/share/gdb/syscalls/mips-n32-linux.xml
#usr/share/gdb/syscalls/mips-n64-linux.xml
#usr/share/gdb/syscalls/mips-o32-linux.xml
#usr/share/gdb/syscalls/netbsd.xml
#usr/share/gdb/syscalls/ppc-linux.xml
#usr/share/gdb/syscalls/ppc64-linux.xml
#usr/share/gdb/syscalls/s390-linux.xml
#usr/share/gdb/syscalls/s390x-linux.xml
#usr/share/gdb/syscalls/sparc-linux.xml
#usr/share/gdb/syscalls/sparc64-linux.xml
#usr/share/gdb/system-gdbinit
#usr/share/gdb/system-gdbinit/elinos.py
#usr/share/gdb/system-gdbinit/wrs-linux.py
#usr/share/info/annotate.info
#usr/share/info/gdb.info
#usr/share/info/gdb.info-1
#usr/share/info/gdb.info-2
#usr/share/info/gdb.info-3
#usr/share/info/gdb.info-4
#usr/share/info/gdb.info-5
#usr/share/info/gdb.info-6
#usr/share/info/gdb.info-7
#usr/share/info/gdb.info-8
#usr/share/info/stabs.info
#usr/share/man/man1/gcore.1
#usr/share/man/man1/gdb-add-index.1
#usr/share/man/man1/gdb.1
#usr/share/man/man1/gdbserver.1
#usr/share/man/man5/gdbinit.5

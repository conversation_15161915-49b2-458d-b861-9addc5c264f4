#etc/fonts
#etc/fonts/conf.d
etc/fonts/conf.d/10-hinting-slight.conf
etc/fonts/conf.d/10-scale-bitmap-fonts.conf
etc/fonts/conf.d/10-sub-pixel-none.conf
etc/fonts/conf.d/10-yes-antialias.conf
etc/fonts/conf.d/11-lcdfilter-default.conf
etc/fonts/conf.d/20-unhint-small-vera.conf
etc/fonts/conf.d/30-metric-aliases.conf
etc/fonts/conf.d/40-nonlatin.conf
etc/fonts/conf.d/45-generic.conf
etc/fonts/conf.d/45-latin.conf
etc/fonts/conf.d/48-spacing.conf
etc/fonts/conf.d/49-sansserif.conf
etc/fonts/conf.d/50-user.conf
etc/fonts/conf.d/51-local.conf
etc/fonts/conf.d/60-generic.conf
etc/fonts/conf.d/60-latin.conf
etc/fonts/conf.d/65-fonts-persian.conf
etc/fonts/conf.d/65-nonlatin.conf
etc/fonts/conf.d/69-unifont.conf
etc/fonts/conf.d/80-delicious.conf
etc/fonts/conf.d/90-synthetic.conf
#etc/fonts/conf.d/README
etc/fonts/fonts.conf
usr/bin/fc-cache
usr/bin/fc-cat
usr/bin/fc-conflist
usr/bin/fc-list
usr/bin/fc-match
usr/bin/fc-pattern
usr/bin/fc-query
usr/bin/fc-scan
usr/bin/fc-validate
#usr/include/fontconfig
#usr/include/fontconfig/fcfreetype.h
#usr/include/fontconfig/fcprivate.h
#usr/include/fontconfig/fontconfig.h
#usr/lib/libfontconfig.la
#usr/lib/libfontconfig.so
usr/lib/libfontconfig.so.1
usr/lib/libfontconfig.so.1.12.1
#usr/lib/pkgconfig/fontconfig.pc
#usr/share/fontconfig
#usr/share/fontconfig/conf.avail
#usr/share/fontconfig/conf.avail/05-reset-dirs-sample.conf
#usr/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf
#usr/share/fontconfig/conf.avail/10-autohint.conf
#usr/share/fontconfig/conf.avail/10-hinting-full.conf
#usr/share/fontconfig/conf.avail/10-hinting-medium.conf
#usr/share/fontconfig/conf.avail/10-hinting-none.conf
#usr/share/fontconfig/conf.avail/10-hinting-slight.conf
#usr/share/fontconfig/conf.avail/10-no-antialias.conf
#usr/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf
#usr/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf
#usr/share/fontconfig/conf.avail/10-sub-pixel-none.conf
#usr/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf
#usr/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf
#usr/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf
#usr/share/fontconfig/conf.avail/10-unhinted.conf
#usr/share/fontconfig/conf.avail/10-yes-antialias.conf
#usr/share/fontconfig/conf.avail/11-lcdfilter-default.conf
#usr/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf
#usr/share/fontconfig/conf.avail/11-lcdfilter-light.conf
#usr/share/fontconfig/conf.avail/11-lcdfilter-none.conf
#usr/share/fontconfig/conf.avail/20-unhint-small-vera.conf
#usr/share/fontconfig/conf.avail/25-unhint-nonlatin.conf
#usr/share/fontconfig/conf.avail/30-metric-aliases.conf
#usr/share/fontconfig/conf.avail/35-lang-normalize.conf
#usr/share/fontconfig/conf.avail/40-nonlatin.conf
#usr/share/fontconfig/conf.avail/45-generic.conf
#usr/share/fontconfig/conf.avail/45-latin.conf
#usr/share/fontconfig/conf.avail/48-spacing.conf
#usr/share/fontconfig/conf.avail/49-sansserif.conf
#usr/share/fontconfig/conf.avail/50-user.conf
#usr/share/fontconfig/conf.avail/51-local.conf
#usr/share/fontconfig/conf.avail/60-generic.conf
#usr/share/fontconfig/conf.avail/60-latin.conf
#usr/share/fontconfig/conf.avail/65-fonts-persian.conf
#usr/share/fontconfig/conf.avail/65-khmer.conf
#usr/share/fontconfig/conf.avail/65-nonlatin.conf
#usr/share/fontconfig/conf.avail/69-unifont.conf
#usr/share/fontconfig/conf.avail/70-no-bitmaps.conf
#usr/share/fontconfig/conf.avail/70-yes-bitmaps.conf
#usr/share/fontconfig/conf.avail/80-delicious.conf
#usr/share/fontconfig/conf.avail/90-synthetic.conf
#usr/share/gettext/its/fontconfig.its
#usr/share/gettext/its/fontconfig.loc
#usr/share/locale/ka/LC_MESSAGES/fontconfig-conf.mo
#usr/share/locale/ka/LC_MESSAGES/fontconfig.mo
#usr/share/locale/zh_CN/LC_MESSAGES/fontconfig-conf.mo
#usr/share/locale/zh_CN/LC_MESSAGES/fontconfig.mo
#usr/share/xml
#usr/share/xml/fontconfig
usr/share/xml/fontconfig/fonts.dtd
var/cache/fontconfig
var/cache/fontconfig/CACHEDIR.TAG

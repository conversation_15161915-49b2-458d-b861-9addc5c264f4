Listen 1010

<VirtualHost *:1010>
	ScriptAlias /icinga/cgi-bin /usr/share/icinga/cgi-bin
	Alias /icinga /usr/share/icinga/

	RewriteEngine on
	RewriteRule ^/$ /icinga/ [R]

	<Directory /usr/share/icinga/cgi-bin>
		Options ExecCGI
		AllowOverride None

		AuthName "Icinga Access"
		AuthType Basic
		AuthUserFile /etc/icinga/htpasswd.users

		Require valid-user
	</Directory>

	<Directory /usr/share/icinga/>
		Options None
		AllowOverride All

		AuthName "Icinga Access"
		AuthType Basic
		AuthUserFile /etc/icinga/htpasswd.users

		Require valid-user
	</Directory>
</VirtualHost>

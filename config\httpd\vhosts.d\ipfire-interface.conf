<VirtualHost *:81>

    DocumentRoot /srv/web/ipfire/html

    RewriteEngine on
    RewriteCond %{REQUEST_METHOD} ^(TRACE|TRACK|OPTIONS)
    RewriteRule .* - [F]

    Header always set X-Content-Type-Options nosniff
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src: 'self' data:"
    Header always set Referrer-Policy strict-origin
    Header always set X-Frame-Options sameorigin

    <Directory /srv/web/ipfire/html>
        Options ExecCGI
        AllowOverride None
        Require all granted
    </Directory>
    <DirectoryMatch "/srv/web/ipfire/html/(graphs|sgraph)">
        Options SymLinksIfOwnerMatch
        RewriteEngine on
        RewriteCond %{HTTPS} off
        RewriteRule (.*) https://%{SERVER_NAME}:444/$1 [R=301,L]
    </DirectoryMatch>
    ScriptAlias /cgi-bin/ /srv/web/ipfire/cgi-bin/
    <Directory /srv/web/ipfire/cgi-bin>
        Options SymLinksIfOwnerMatch
        RewriteEngine on
        RewriteCond %{HTTPS} off
        RewriteRule (.*) https://%{SERVER_NAME}:444/$1 [R=301,L]
    </Directory>
    Alias /updatecache/ /var/updatecache/
	<Directory /var/updatecache>
		 Options ExecCGI
		 AllowOverride None
		 Require all granted
	</Directory>
    Alias /repository/ /var/urlrepo/
	<Directory /var/urlrepo>
		 Options ExecCGI
		 AllowOverride None
		 Require all granted
	</Directory>
    Alias /wpad.dat /srv/web/ipfire/html/proxy.pac
</VirtualHost>

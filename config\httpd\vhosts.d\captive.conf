Listen 1013

<VirtualHost *:1013>
	DocumentRoot /srv/web/ipfire/html/captive

	# Close all connections as soon as a reply has been sent.
	# Most browsers open loads of connections which then causes
	# the access page being loaded again after a correct coupon
	# code was entered.
	KeepAlive Off

	Header always set X-Content-Type-Options nosniff
	Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'"
	Header always set Referrer-Policy strict-origin
	Header always set X-Frame-Options sameorigin

	ScriptAlias /cgi-bin/ /srv/web/ipfire/cgi-bin/captive/
	Alias /assets/ /srv/web/ipfire/html/captive/assets/

	Alias /favicon.ico /srv/web/ipfire/html/captive/assets/favicon.ico

	# All unknown URIs will be redirected to the first
	# redirector script.
	ScriptAliasMatch .* /srv/web/ipfire/cgi-bin/captive/redirect.cgi

	<Directory /srv/web/ipfire/cgi-bin/captive>
		Options ExecCGI
		Require all granted
	</Directory>

	<Directory /srv/web/ipfire/html/captive>
		Options +FollowSymlinks
		Require all granted
	</Directory>
</VirtualHost>

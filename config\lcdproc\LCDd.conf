# LCDd.conf -- configuration file for the LCDproc server daemon LCDd
#
# This file contains the configuration for the LCDd server.
#
# The format is ini-file-like. It is divided into sections that start at
# markers that look like [section]. Comments are all line-based comments,
# and are lines that start with '#' or ';'.
#
# The server has a 'central' section named [server]. For the menu there is
# a section called [menu]. Further each driver has a section which
# defines how the driver acts.
#
# The drivers are activated by specifying them in a driver= line in the
# server section, like:
#
#   Driver=curses
#
# This tells LCDd to use the curses driver.
# The first driver that is loaded and is capable of output defines the
# size of the display. The default driver to use is curses.
# If the driver is specified using the -d <driver> command line option,
# the Driver= options in the config file are ignored.
#
# The drivers read their own options from the respective sections.

## Server section with all kinds of settings for the LCDd server ##
[server]

# Where can we find the driver modules ?
# IMPORTANT: Make sure to change this setting to reflect your
#            specific setup! Otherwise LCDd won't be able to find
#            the driver modules and will thus not be able to
#            function properly.
# NOTE: Always place a slash as last character !
DriverPath=/usr/lib/lcdproc/

# Tells the server to load the given drivers. Multiple lines can be given.
# The name of the driver is case sensitive and determines the section
# where to look for further configuration options of the specific driver
# as well as the name of the dynamic driver module to load at runtime.
# The latter one can be changed by giving a File= directive in the
# driver specific section.
#
# The following drivers are supported:
#   bayrad, CFontz, CFontz633, CFontzPacket, curses, CwLnx, ea65,
#   EyeboxOne, g15, glcdlib, glk, hd44780, icp_a106, imon, imonlcd,
#   IOWarrior, irman, joy, lb216, lcdm001, lcterm, lirc, lis, MD8800,
#   mdm166a, ms6931, mtc_s16209x, MtxOrb, mx5000, NoritakeVFD, picolcd,
#   pyramid, sed1330, sed1520, serialPOS, serialVFD, shuttleVFD, sli,
#   stv5730, svga, t6963, text, tyan, ula200, xosd
Driver=CFontz

# Tells the driver to bind to the given interface
Bind=127.0.0.1

# Listen on this specified port; defaults to 13666.
Port=13666

# Sets the reporting level; defaults to 2 (warnings and errors only).
#ReportLevel=3

# Should we report to syslog instead of stderr ? [default: no; legal: yes, no]
#ReportToSyslog=yes

# User to run as.  LCDd will drop its root privileges, if any,
# and run as this user instead.
User=root

# The server will stay in the foreground if set to true.
#Foreground=no

# Hello message: each entry represents a display line; default: builtin
#Hello="  Welcome to"
#Hello="   LCDproc!"

# GoodBye message: each entry represents a display line; default: builtin
#GoodBye="Thanks for using"
#GoodBye="   LCDproc!"

# Sets the default time in seconds to displays a screen.
WaitTime=10

# If set to no, LCDd will start with screen rotation disabled. This has the
# same effect as if the ToggleRotateKey had been pressed. Rotation will start
# if the ToggleRotateKey is pressed. Note that this setting does not turn off
# priority sorting of screens. [default: on; legal: on, off]
#AutoRotate=no

# If yes, the the serverscreen will be rotated as a usual info screen. If no,
# it will be a background screen, only visible when no other screens are
# active. The special value 'blank' is similar to no, but only a blank screen
# is displayed. [default: on; legal: on, off, blank]
#ServerScreen=no

# Set master backlight setting. If set to 'open' a client may control the
# backlight for its own screens (only). [default: open; legal: off, open, on]
#Backlight=open

# Set master heartbeat setting. If set to 'open' a client may control the
# heartbeat for its own screens (only). [default: open; legal: off, open, on]
#Heartbeat=open

# set title scrolling speed [default: 10; legal: 0-10]
#TitleSpeed=10

# The "...Key=" lines define what the server does with keypresses that
# don't go to any client. The ToggleRotateKey stops rotation of screens, while
# the PrevScreenKey and NextScreenKey go back / forward one screen (even if
# rotation is disabled.
# Assign the key string returned by the driver to the ...Key setting. These
# are the defaults:
ToggleRotateKey=Enter
PrevScreenKey=Left
NextScreenKey=Right
#ScrollUpKey=Up
#ScrollDownKey=Down


## The menu section. The menu is an internal LCDproc client. ##
[menu]
# You can configure what keys the menu should use. Note that the MenuKey
# will be reserved exclusively, the others work in shared mode.

# Up to six keys are supported. The MenuKey (to enter and exit the menu), the
# EnterKey (to select values) and at least one movement keys are required.
# These are the default key assignments:
MenuKey=Escape
EnterKey=Enter
UpKey=Up
DownKey=Down
#LeftKey=Left
#RightKey=Right


### Driver sections are below this line, in alphabetical order  ###


## EMAC BayRAD driver ##
[bayrad]

# Select the output device to use [default: /dev/lcd]
Device=/dev/lcd

# Set the communication speed [default: 9600; legal:  1200, 2400, 9600, 19200]
Speed=9600



## CrystalFontz driver (for CF632 & CF634) ##
[CFontz]

# Select the output device to use [default: /dev/lcd]
Device=/dev/ttyS0
# Select the LCD size [default: 20x4]
Size=20x4
# Set the initial contrast [default: 560; legal: 0 - 1000]
Contrast=560
# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=800
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=800
# Set the communication speed [default: 9600; legal: 1200, 2400, 9600, 19200,
# 115200]
Speed=19200
# Set the firmware version (New means >= 2.0) [default: no; legal: yes, no]
NewFirmware=yes
# Reinitialize the LCD's BIOS [default: no; legal: yes, no]
# normally you shouldn't need this
Reboot=no



## CrystalFontz633 driver (for CF633 only) ##
[CFontz633]
# Note: Use of this driver is deprecated, please use CFontzPacket driver
# with Model=633 instead.

# Select the output device to use [default: /dev/lcd]
Device=/dev/ttyS0
# Select the LCD type (size) [default: 16x2]
Size=16x2
# Set the initial contrast [default: 560; legal: 0 - 1000]
Contrast=350
# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=1000
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=50
# Set the communication speed [default: 9600; legal: 1200, 2400, 9600, 19200, 115200]
Speed=19200
# Set the firmware version (New means >= 2.0) [default: no; legal: yes, no]
# Currently this flag is not in use, there is no such thing as NewFirmware. ;=)
#NewFirmware=no
# Reinitialize the LCD's BIOS [default: no; legal: yes, no]
# I want to reboot the LCD to make sure we start from a known state
Reboot=yes



## CrystalFontz packet driver (for CFA533, CFA631, CFA633 & CFA635) ##
[CFontzPacket]

# Select the LCD model [default: 633; legal: 533, 631, 633, 635]
Model=633

# Select the output device to use [default: /dev/lcd]
Device=/dev/ttyUSB0

# Set the initial contrast [default: 560; legal: 0 - 1000]
Contrast=350

# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=1000

# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=50

# Reinitialize the LCD's BIOS on driver start. [default: no; legal: yes, no]
Reboot=yes

# Enable the USB flag if the device is connected to an USB port. For
# serial ports leave it disabled. [default: no; legal: yes, no]
#USB=yes

# Very old 633 firmware versions do not support partial screen updates using
# 'Send Data to LCD' command (31). For those devices it may be necessary to
# enable this flag. [default: no; legal: yes, no]
#OldFirmware=yes

# Override the LCD size known for the selected model. Usually setting this
# value should not be necessary.
#Size=20x4

# Override the default communication speed known for the selected model.
# Default value depends on model [legal: 19200, 115200]
#Speed=115200



## Curses driver ##
[curses]

# color settings
# foreground color [default: blue]
Foreground=blue
# background color when "backlight" is off [default: cyan]
Background=cyan
# background color when "backlight" is on [default: red]
Backlight=red

# display size [default: 20x4]
Size=20x4

# What position (X,Y) to start the left top corner at...
# Default: (7,7)
TopLeftX=7
TopLeftY=7

# use ASC symbols for icons & bars [default: no; legal: yes, no]
UseACS=no

# draw Border [default: yes; legal: yes, no]
DrawBorder=yes



## Cwlinux driver ##
[CwLnx]

# Select the LCD model [default: 12232; legal: 12232, 12832, 1602]
Model=12232

# Select the output device to use [default: /dev/lcd]
Device=/dev/ttyUSB0

# Select the LCD size. Default depends on model:
# 12232: 20x4
# 12832: 21x4
# 1602: 16x2
Size=20x4

# Set the communication speed [default: 19200; legal: 9600, 19200]
Speed=19200

# Reinitialize the LCD's BIOS [default: no; legal: yes, no]
# normally you shouldn't need this
Reboot=no

# If you have a keypad connected. Keypad layout is currently not
# configureable from the config file.
Keypad=yes

# If you have a non-standard keypad you can associate any keystrings to keys.
# There are 6 input keys in the CwLnx hardware that generate characters
# from 'A' to 'F'.
#
# The following is the built-in default mapping hardcoded in the driver.
# You can leave those unchanged if you have a standard keypad.
# You can change it if you want to report other keystrings or have a non
# standard keypad.
# KeyMap_A=Up
# KeyMap_B=Down
# KeyMap_C=Left
# KeyMap_D=Right
# KeyMap_E=Enter
# KeyMap_F=Escape

# keypad_test_mode permits one to test keypad assignment
# Default value is no
#keypad_test_mode=yes



## ea65 driver for the display in AOpen XC Cube AV EA65 media barebones ##
[ea65]

# Device is fixed /dev/ttyS1
# Width and Height are fixed 9x1

# As the VFD is self luminescent we don't have a backlight
# But we can use the backlight functions to control the front LEDs
# Brightness 0 to 299 -> LEDs off
# Brightness 300 to 699 -> LEDs half bright
# Brightness 700 to 1000 -> LEDs full bright
Brightness=500
# OffBrightness is the the value used for the 'backlight off' state
OffBrightness=0



## EyeboxOne driver ##
[EyeboxOne]

# Select the output device to use [default: /dev/ttyS1]
#Device=/dev/cua01
Device=/dev/ttyS1

# Set the display size [default: 20x4]
Size=20x4

# Switch on the backlight? [default: yes]
Backlight=yes

# Switch on the cursor? [default: no]
Cursor=no

# Set the communication speed [default: 19200; legal: 1200, 2400, 9600, 19200]
Speed=19200

# Enter Key is a \r character, so it's hardcoded in the driver
LeftKey=D
RightKey=C
UpKey=A
DownKey=B
EscapeKey=P

# You can find out which key of your display sends which
# character by setting keypad_test_mode to yes and running
# LCDd. LCDd will output all characters it receives.
# Afterwards you can modify the settings above and set
# keypad_set_mode to no again.
keypad_test_mode=no



## g15 driver for Logitech G15 Keyboard LCDs ##
[g15]

# Display size (currently unused)
size=20x5



## glcdlib meta driver for graphical LCDs ##
[glcdlib]

## mandatory:

# which graphical display supported by graphlcd-base to use [default: image]
# (see /etc/graphlcd.conf for possible drivers)
Driver=t6963c

# no=use graphlcd bitmap fonts (they have only one size / font file)
# yes=use fonts supported by FreeType2 (needs Freetype2 support in
# libglcdprocdriver and its dependants)
UseFT2=no

# text resolution in fixed width characters [default: 16x4]
# (if it won't fit according to available physical pixel resolution
# and the minimum available font face size in pixels, then
# 'DebugBorder' will automatically be turned on)
TextResolution=20x4

# path to font file to use
#FontFile=/usr/share/fonts/corefonts/courbd.ttf

## these only apply if UseFT2=yes:

# character encoding to use
CharEncoding=iso8859-2

# minimum size in pixels in which fonts should be rendered
MinFontFaceSize=7x12

## optional:
Brightness=50                   # Brightness (in %) if applicable
Contrast=50                     # Contrast (in %) if applicable
Backlight=no                    # Backlight if applicable
UpsideDown=no                   # flip image upside down
Invert=no                       # invert light/dark pixels
ShowDebugFrame=no               # turns on/off 1 pixel thick debugging
                                # border within the usable text area,
                                # for setting up TextResolution and
                                # MinFontFaceSize (if using FT2);
ShowBigBorder=no                # border around the unused area
ShowThinBorder=yes              # border around the unused area
PixelShiftX=0
PixelShiftY=2



## Matrix Orbital GLK driver ##
[glk]

# select the serial device to use [default: /dev/lcd]
Device=/dev/lcd

# set the initial contrast value [default: 560; legal: 0 - 1000]
Contrast=560

# set the serial port speed [default: 19200; legal: 9600, 19200, 38400]
Speed=19200



## Hitachi HD44780 driver ##
[hd44780]

# Select what type of connection. See documentation for types.
ConnectionType=4bit

# Port where the LPT is. Usual value are: 0x278, 0x378 and 0x3BC
Port=0x378

# Device of the serial interface [default: /dev/lcd]
Device=/dev/ttyS0

# Bitrate of the serial port (0 for interface default)
Speed=0

# If you have a keypad connected.
# You may also need to configure the keypad layout further on in this file.
Keypad=no

# Set the initial contrast (bwctusb and lcd2usb) [default: 500; legal: 0 - 1000]
Contrast=0

# Set brightness of the backlight (lcd2usb only) [default: 0; legal: 0 - 1000]
#Brightness=1000
#OffBrightness=0

# If you have a switchable backlight.
Backlight=no

# If you have the additional output port ("bargraph") and you want to
# be able to control it with the lcdproc OUTPUT command
OutputPort=no

# Specifies if the last line is pixel addressable (yes) or it controls an
# underline effect (no). [default: yes; legal: yes, no]
#Lastline=yes

# Specifies the size of the LCD.
# In case of multiple combined displays, this should be the total size.
Size=20x4

# For multiple combined displays: how many lines does each display have.
# Vspan=2,2 means both displays have 2 lines.
#vspan=2,2

# If you have an HD66712, a KS0073 or another 'almost HD44780-compatible',
# set this flag to get into extended mode (4-line linear).
#ExtendedMode=yes

# In extended mode, on some controllers like the ST7036 (in 3 line mode)
# the next line in DDRAM won't start 0x20 higher. [default: 0x20]
#LineAddress=0x10

# Character map to to map ISO-8859-1 to the LCD's character set
# [default: hd44780_default; legal: hd44780_default, hd44780_euro, ea_ks0073,
# sed1278f_0b, hd44780_koi8_r, hd44780_cp1251, hd44780_8859_5, upd16314 ]
# (hd44780_koi8_r, hd44780_cp1251, hd44780_8859_5 and upd16314 are possible if
# compiled with additional charmaps)
CharMap=hd44780_default

# If your display is slow and cannot keep up with the flow of data from
# LCDd, garbage can appear on the LCDd. Set this delay factor to 2 or 4
# to increase the delays. Default: 1.
#DelayMult=2

# Some displays (e.g. vdr-wakeup) need a message from the driver to that it
# is still alive. When set to a value bigger then null the character in the
# upper left corner is updated every <KeepAliveDisplay> seconds. Default: 0.
#KeepAliveDisplay=0

# If you experience occasional garbage on your display you can use this
# option as workaround. If set to a value bigger than null it forces a
# full screen refresh <RefreshDiplay> seconds. Default: 0.
#RefreshDisplay=5

# You can reduce the inserted delays by setting this to false.
# On fast PCs it is possible your LCD does not respond correctly.
# Default: true.
DelayBus=true

# If you have a keypad you can assign keystrings to the keys.
# See documentation for used terms and how to wire it.
# For example to give directly connected key 4 the string "Enter", use:
#   KeyDirect_4=Enter
# For matrix keys use the X and Y coordinates of the key:
#   KeyMatrix_1_3=Enter
KeyMatrix_4_1=Enter
KeyMatrix_4_2=Up
KeyMatrix_4_3=Down
KeyMatrix_4_4=Escape



## ICP A106 driver ##
[icp_a106]
Device=/dev/ttyS1



## Code Mercenaries IO-Warrior driver ##
[IOWarrior]

# display dimensions
Size=20x4

# serial number. Must be exactly as listed by usbview
# (if not given, the 1st IOWarrior found gets used)
#SerialNumber=00000674

# If you have an HD66712, a KS0073 or another 'almost HD44780-compatible',
# set this flag to get into extended mode (4-line linear).
#ExtendedMode=yes

# Specifies if the last line is pixel addressable (yes) or it controls an
# underline effect (no). [default: yes; legal: yes, no]
#Lastline=yes



## Soundgraph/Ahanix/Silverstone/Uneed/Accent iMON driver ##
[imon]

# select the device to use
Device=/dev/lcd0

# display dimensions
Size=16x2

# Character map to to map ISO-8859-1 to the displays character set.
# [default: none; legal: none, hd44780_euro, upd16314, hd44780_koi8_r,
# hd44780_cp1251, hd44780_8859_5 ] (upd16314, hd44780_koi8_r,
# hd44780_cp1251, hd44780_8859_5 are possible if compiled with additional
# charmaps)
CharMap=hd44780_euro

## Soundgraph iMON LCD ##
[imonlcd]
# Specify which iMon protocol should be used
# [legal: 0, 1; default: 0]
# Choose 0 for 15c2:ffdc device,
# Choose 1 for 15c2:0038 device
Protocol=0

# Set the exit behavior [legal: 0-2; default: 1]
# 0 means leave shutdown message,
# 1 means show the big clock,
# 2 means blank device
#OnExit=2

# Select the output device to use [default: /dev/lcd0]
Device=/dev/lcd0

# Select the displays contrast [default: 200; legal: 0-1000]
Contrast=200

# Specify the size of the display in pixels [default: 96x16]
#Size=96x16

# Set the backlight state [default: on; legal: on, off]
#Backlight=on

# Set the disc mode [legal: 0,1; default: 0]
# 0 => spin the "slim" disc - two disc segments,
# 1 => their complement spinning;
#DiscMode=0



## IrMan driver ##
[IrMan]
# in case of trouble with IrMan, try the Lirc emulator for IrMan

# Select the input device to use
#Device=/dev/irman

# Select the configuration file to use
#Config=/etc/irman.cfg



## IRtrans driver ##
[irtrans]

# Does the device have a backlight? [default: no; legal: yes, no]
#Backlight=no

# IRTrans device to connect to [default: localhost]
#Hostname=localhost

# display dimensions
Size=16x2



## Joystick driver ##
[joy]

# Select the input device to use [default: /dev/js0]
Device=/dev/js0

# set the axis map
Map_Axis1neg=Left
Map_Axis1pos=Right
Map_Axis2neg=Up
Map_Axis2pos=Down

# set the button map
Map_Button1=Enter
Map_Button2=Escape



## LB216 driver ##
[lb216]

# Select the output device to use [default: /dev/lcd]
Device=/dev/lcd

# Set the initial brightness [default: 255; legal: 0 - 255]
Brightness=255

# Set the communication speed [default: 9600; legal: 2400, 9600]
Speed=9600

# Reinitialize the LCD's BIOS [default: no; legal: yes, no]
Reboot=no



## LCDM001 driver ##
[lcdm001]

Device=/dev/ttyS1

# keypad settings
# Keyname      Function
#              Normal context              Menu context
# -------      --------------              ------------
# PauseKey     Pause/Continue              Enter/select
# BackKey      Back(Go to previous screen) Up/Left
# ForwardKey   Forward(Go to next screen)  Down/Right
# MainMenuKey  Open main menu              Exit/Cancel
PauseKey=LeftKey
BackKey=UpKey
ForwardKey=DownKey
MainMenuKey=RightKey

# You can rearrange the settings here.
# If your device is broken, have a look at server/drivers/lcdm001.h



## HNE LCTerm driver ##
[lcterm]
Device=/dev/ttyS1
Size=16x2



## LIRC input driver ##
[lirc]

# Specify an alternative location of the lircrc file [default: ~/.lircrc]
#lircrc=/etc/lircrc.lcdproc

# Must be the same as in your lircrc
#prog=lcdd



## LIS MCE 2005 driver ##
[lis]

# Set the initial brightness [default: 1000; legal: 0 - 1000]
# 0-250 = 25%, 251-500 = 50%, 501-750 = 75%, 751-1000 = 100%
#Brightness=1000

# Columns by lines [default: 20x2]
#Size=20x2

# USB Vendor ID [default: 0x0403]
# Change only if testing a compatible device.
#VendorID=0x0403

# USB Product ID [default: 0x6001]
# Change only if testing a compatible device.
#ProductID=0x6001



##The driver for the VFD of the Medion MD8800 PC ##
[MD8800]
# device to use [default: /dev/ttyS1]
#Device=/dev/ttyS1

# display size [default: 16x2]
#Size=16x2

# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=1000
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=50



## Futuba MDM166A Display
[mdm166a]
# Show self-running clock after LCDd shutdown
# Possible values: [default: no; legal: no, small, big]
Clock=big
# Dim display, no dimming gives full brightness [default: no, legal: yes, no]
Dimming=no
# Dim display in case LCDd is inactive [default: no, legal: yes, no]
OffDimming=yes



## MSI MS-6931 driver for displays in 1HU servers ##
[ms6931]

# device to use [default: /dev/ttyS1]
Device=/dev/ttyS1

# display size [default: 16x2]
#Size=16x2



## MTC-S16209x driver ##
[mtc_s16209x]

# Select the output device to use [default: /dev/lcd]
Device=/dev/lcd

# Set the initial brightness [default: 255; legal: 0 - 255]
Brightness=255

# Reinitialize the LCD's BIOS [default: no; legal: yes, no]
Reboot=no



## Matrix Orbital driver ##
[MtxOrb]

# Select the output device to use [default: /dev/lcd]
Device=/dev/ttyS0

# Set the display size [default: 20x4]
Size=20x4

# Set the display type [default: lcd; legal: lcd, lkd, vfd, vkd]
Type=lkd

# Set the initial contrast [default: 480]
# NOTE: The driver will ignore this if the display
#       is a vfd or vkd as they don't have this feature
Contrast=480

# Some old displays do not have an adjustable backlight but only can
# switch the backlight on/off. If you experience randomly appearing block
# characters, try setting this to false. [default: yes; legal: yes, no]
hasAdjustableBacklight=no

# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=1000
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=0

# Set the communication speed [default: 19200; legal: 1200, 2400, 9600, 19200]
Speed=19200

# The following table translates from MtxOrb key letters to logical key names.
# By default no keys are mapped, meaning the keypad is not used at all.
#KeyMap_A=Left
#KeyMap_B=Right
#KeyMap_C=Up
#KeyMap_D=Down
#KeyMap_E=Enter
#KeyMap_F=Escape
# See the [menu] section for an explanation of the key mappings

# You can find out which key of your display sends which
# character by setting keypad_test_mode to yes and running
# LCDd. LCDd will output all characters it receives.
# Afterwards you can modify the settings above and set
# keypad_set_mode to no again.
keypad_test_mode=no



## mx5000 driver for LCD display on the Logitech MX5000 keyboard ##
[mx5000]

# Select the output device to use [default: /dev/hiddev0]
Device = /dev/hiddev0
# Time to wait in ms after the refresh screen has been sent [default: 1000]
WaitAfterRefresh = 1000



## Noritake VFD driver ##
[NoritakeVFD]
# device where the VFD is. Usual values are /dev/ttyS0 and /dev/ttyS1
# [default: /dev/lcd]
Device=/dev/ttyS0
# Specifies the size of the LCD.
Size=20x4
# Set the initial brightness [default: 1000; legal: 0 - 1000]
Brightness=1000
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=50
# set the serial port speed [default: 9600, legal: 1200, 2400, 9600, 19200, 115200]
Speed=9600
# Set serial data parity [default: 0; legal: 0-2 ]
# Meaning: 0(=none), 1(=odd), 2(=even)
Parity=0
# re-initialize the VFD [default: no; legal: yes, no]
Reboot=no



## Mini-box.com picoLCD (usblcd) driver ##
[picolcd]

# KeyTimeout is the time in ms that LCDd spends waiting for a key press before
# cycling through other duties.  Higher values make LCDd use less CPU time and
# make key presses more detectable.  Lower values make LCDd more responsive
# but a little prone to missing key presses.  500 (.5 second) is the default
# and a balanced value. [default: 500; legal: 0 - 1000]
KeyTimeout=500

# Sets the initial state of the backlight upon start-up.
# [default: on; legal: on, off]
#Backlight=on

# Set the initial brightness [default: 1000; legal: 0 - 1000]. Works only
# with the 20x4 device
Brightness=1000

# Set the brightness while the backlight is 'off' [default: 0; legal: 0 - 1000].
# Works only with the 20x4 device.
#OffBrightness=0

# Set the initial contrast [default: 1000; legal: 0 - 1000]
Contrast=1000

# Light the keys? [default: on; legal: on, off]
Keylights=on

# If Keylights is on, the you can unlight specific keys below:
# Key0 is the directional pad.  Key1 - Key5 correspond to the F1 - F5 keys.
# There is no LED for the +/- keys.  This is a handy way to indicate to users
# which keys are disabled.  [default: on; legal: on, off]
Key0Light=on
Key1Light=on
Key2Light=on
Key3Light=on
Key4Light=on
Key5Light=on

# Host name or IP address of the LIRC instance that is to receive IR codes
# If not set, or set to an empty value, IR support is disabled.
#LircHost=127.0.0.1

# UDP port on which LIRC is listening [default: 8765; legal: 1 - 65535]
LircPort=8765

# Threshold in jiffies of synthesized gap that triggers flushing the IR data
# to lirc [default: 100 ; max: 32767 ]
# 100 means 6.1ms. legal: 16 - 32767; Use 0 to disable.
LircFlushThreshold=100



## Pyramid LCD driver ##
[pyramid]

# device to connect to [default: /dev/lcd]
Device=/dev/ttyUSB0



## Seiko Epson 1330 driver ##
[sed1330]

# Port where the LPT is. Common values are 0x278, 0x378 and 0x3BC
Port=0x378

# Type of LCD module (legal: G321D, G121C, G242C, G191D, G2446, SP14Q002)
# Note: Currently only tested with G321D & SP14Q002.
Type=G321D

# Width x Height of a character cell in pixels [legal: 6x7 - 8x16; default: 6x10]
CellSize=6x10

# Select what type of connection [legal: classic, bitshaker; default: classic]
ConnectionType=classic



## Seiko Epson 1520 driver ##
[sed1520]

# Port where the LPT is. Usual values are 0x278, 0x378 and 0x3BC
Port=0x378

# Select the interface type (wiring) for the display. Supported values are
# 68 for 68-style connection (RESET level high) and 80 for 80-style connection
# (RESET level low). [legal: 68, 80; default: 80]
InterfaceType=68

# On fast machines it may be necessary to slow down transfer to the display.
# If this value is set to zero, delay is disabled. Any value greater than
# zero slows down each write by one microsecond. [legal: 0-1000; default: 1]
DelayMult=0


## serial POS display driver ##
[serialPOS]

# Device to use in serial mode [default: /dev/lcd]
Device=/dev/lcd

# Specifies the size of the display in characters. [default: 16x2]
Size=16x2

# Set the communication protocol to use with the POS display.
# [default: AEDEX; legal: IEE, Epson, Emax, IBM, LogicControls, Ultimate]
Type=AEDEX

# communication baud rate with the display [default: 9600; legal: 1200, 2400,
# 19200, 115200]
Speed=9600



## Serial VFD driver ##
## Drives various (see below) serial 5x7dot VFD's.  ##
[serialVFD]

# Specifies the displaytype.[default: 0]
# 0 NEC (FIPC8367 based) VFDs.
# 1 KD Rev 2.1.
# 2 Noritake VFDs (*).
# 3 Futaba VFDs
# 4 IEE S03601-95B
# 5 IEE S03601-96-080 (*)
# 6 Futaba NA202SD08FA (allmost IEE compatible)
# 7 Samsung 20S207DA4 and 20S207DA6
# 8 Nixdorf BA6x / VT100
# (* most should work, not tested yet.)
Type=0

# "no" if display connected serial, "yes" if connected parallel. [default: no]
# I.e. serial by default
use_parallel=no

# Number of Custom-Characters. default is display type dependent
#Custom-Characters=0

# Portaddress where the LPT is. Used in parallel mode only. Usual values are
# 0x278, 0x378 and 0x3BC.
Port=0x378

# Set parallel port timing delay (us). Used in parallel mode only.
# [default: 2; legal: 0 - 255]
#PortWait=2

# Device to use in serial mode. Usual values are /dev/ttyS0 and /dev/ttyS1
Device=/dev/ttyS1

# Specifies the size of the VFD.
Size=20x2

# Set the initial brightness [default: 1000; legal: 0 - 1000]
# (4 steps 0-250, 251-500, 501-750, 751-1000)
Brightness=1000
# Set the initial off-brightness [default: 0; legal: 0 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
# (4 steps 0-250, 251-500, 501-750, 751-1000)
OffBrightness=0

# set the serial port speed [default: 9600; legal: 1200, 2400, 9600, 19200, 115200]
Speed=9600

# enable ISO 8859 1 compatibility [default: yes; legal: yes, no]
#ISO_8859_1=yes



## shuttleVFD driver ##
[shuttleVFD]
# No options



## stv5730 driver ##
[stv5730]

# Port the device is connected to [default: 0x378]
Port=0x378


[SureElec]

# Port the device is connected to  (by default first USB serial port)
Device=/dev/ttyUSB0

# Edition level of the device (can be 1, 2 or 3) [default: 2]
#Edition=1

# set display size
# Note: The size can be obtained directly from device for edition 2 & 3.
#Size=16x2

# Set the initial contrast [default: 480; legal: 0 - 1000]
#Contrast=200

# Set the initial brightness [default: 480; legal: 1 - 1000]
#Brightness=480

# Set the initial off-brightness [default: 100; legal: 1 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
#OffBrightness=100


## SVGAlib driver ##
[svga]

# svgalib mode to use [default: G320x240x256 ]
# legal values are supported svgalib modes
#Mode=G640x480x256

# set display size [default: 20x4]
Size=20x4

# Set the initial contrast [default: 500; legal: 0 - 1000]
# Can be set but does not change anything internally
Contrast=500

# Set the initial brightness [default: 1000; legal: 1 - 1000]
Brightness=1000

# Set the initial off-brightness [default: 500; legal: 1 - 1000]
# This value is used when the display is normally
# switched off in case LCDd is inactive
OffBrightness=500



## Text driver ##
[text]
# Set the display size [default: 20x4]
Size=20x4



## Toshiba T6963 driver ##
[t6963]

# set display size in pixels [default: 128x64]
Size=240x128

# port to use [default: 0x378; legal: 0x200 - 0x400]
Port=0x378

# Use LPT port in bi-directional mode. This should work on most LPT port and
# is required for proper timing! [default: yes; legal: yes, no]
bidirectional=yes

# Insert additional delays into reads / writes. [default: no; legal: yes, no]
#delayBus=no

# Clear graphic memory on start-up. [default: no; legal: yes, no]
ClearGraphic=yes



## Tyan Barebones LCD driver (GS10 & GS12 series) ##
[tyan]

# Select the output device to use [default: /dev/lcd]
Device=/dev/lcd

# Set the communication speed [default: 9600; legal: 4800, 9600]
Speed=9600

# set display size [default: 16x2]
Size=16x2



## ELV ula200 driver ##
[ula200]

# Select the LCD size [default: 20x4]
Size=20x4

# If you have a non standard keypad you can associate any keystrings to keys.
# There are 6 input key in the CwLnx hardware that generate characters
# from 'A' to 'F'.
#
# The following it the built-in default mapping hardcoded in the driver.
# You can leave those unchanged if you have a standard keypad.
# You can change it if you want to report other keystrings or have a non
# standard keypad.
# KeyMap_A=Up
# KeyMap_B=Down
# KeyMap_C=Left
# KeyMap_D=Right
# KeyMap_E=Enter
# KeyMap_F=Escape



## Wirz SLI LCD driver ##
[sli]

# Select the output device to use [default: /dev/lcd]
Device=/dev/lcd

# Set the communication speed [default: 19200; legal: 1200, 2400, 9600, 19200,
# 38400, 57600, 115200]
Speed=19200



## OnScreen Display using libxosd ##
[xosd]

# set display size [default: 20x4]
Size=20x4

# Offset in pixels from the top-left corner of the monitor [default: 0x0]
Offset=200x200

# X font to use, in XLFD format, as given by "xfontsel"
Font=-*-terminus-*-r-*-*-*-320-*-*-*-*-*

# EOF

#
# IPFire inittab
#
# Begin /etc/inittab

id:3:initdefault:

si::sysinit:/etc/rc.d/init.d/rc sysinit

l0:0:wait:/etc/rc.d/init.d/rc 0
l1:S1:wait:/etc/rc.d/init.d/rc 1
l2:2:wait:/etc/rc.d/init.d/rc 2
l3:3:wait:/etc/rc.d/init.d/rc 3
l4:4:wait:/etc/rc.d/init.d/rc 4
l5:5:wait:/etc/rc.d/init.d/rc 5
l6:6:wait:/etc/rc.d/init.d/rc 6

ca:12345:ctrlaltdel:/sbin/shutdown -t1 -a -r now

su:S016:once:/sbin/sulogin

1:2345:respawn:/sbin/agetty console --noclear
2:2345:respawn:/sbin/agetty tty2
3:2345:respawn:/sbin/agetty tty3
4:2345:respawn:/sbin/agetty tty4
5:2345:respawn:/sbin/agetty tty5
6:2345:respawn:/sbin/agetty tty6

# End /etc/inittab

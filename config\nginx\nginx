#!/bin/sh
# Begin $rc_base/init.d/nginx

# Based on sysklogd script from LFS-3.1 and earlier.
# Rewritten by <PERSON>  - <EMAIL>

#$LastChangedBy: bdubbs $
#$Date: 2005-08-01 14:29:19 -0500 (Mon, 01 Aug 2005) $

. /etc/sysconfig/rc
. $rc_functions

case "$1" in
	start)
		boot_mesg "Starting nginx Server..."
		loadproc /usr/sbin/nginx
		;;

	stop)
		boot_mesg "Stopping nginx Server..."
		killproc /usr/sbin/nginx
		;;

	reload)
		boot_mesg "Reloading nginx Server..."
		reloadproc /usr/sbin/nginx
		;;

	restart)
		$0 stop
		sleep 1
		$0 start
		;;

	status)
		statusproc /usr/sbin/nginx
		;;

	*)
		echo "Usage: $0 {start|stop|reload|restart|status}"
		exit 1
		;;
esac

# End $rc_base/init.d/nginx

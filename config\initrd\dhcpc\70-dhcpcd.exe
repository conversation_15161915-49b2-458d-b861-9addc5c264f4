#/bin/bash
########################################################################
# Begin
#
# Description : DHCP Client Script (initrd version)
#
# Authors     : <PERSON><PERSON> - <EMAIL>
#
# Version     : 02.00
#
# Notes       : 
#
########################################################################

dhcpcd_up()
{
	set | grep "^new_" | sed "s|^new_||g" | \
	sed "s|'||g" | \
	sort > /var/ipfire/dhcpc/dhcpcd-$interface.info

	DNS=`grep "domain_name_servers" /var/ipfire/dhcpc/dhcpcd-$interface.info | cut -d"=" -f2`
	DNS1=`echo $DNS | cut -d" " -f1`
	DNS2=`echo $DNS | cut -d" " -f2`

	echo "nameserver $DNS1" > /etc/resolv.conf
	echo "nameserver $DNS2" >> /etc/resolv.conf

}

case "$reason" in
BOUND|INFORM|REBIND|REBOOT|RENEW|TIMEOUT|STATIC)	dhcpcd_up;;
esac

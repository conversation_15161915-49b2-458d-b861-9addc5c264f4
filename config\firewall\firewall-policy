#!/bin/sh
###############################################################################
#                                                                             #
# IPFire.org - A linux based firewall                                         #
# <AUTHOR> <EMAIL>                     #
#                                                                             #
# This program is free software: you can redistribute it and/or modify        #
# it under the terms of the GNU General Public License as published by        #
# the Free Software Foundation, either version 3 of the License, or           #
# (at your option) any later version.                                         #
#                                                                             #
# This program is distributed in the hope that it will be useful,             #
# but WITHOUT ANY WARRANTY; without even the implied warranty of              #
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the               #
# GNU General Public License for more details.                                #
#                                                                             #
# You should have received a copy of the GNU General Public License           #
# along with this program.  If not, see <http://www.gnu.org/licenses/>.       #
#                                                                             #
###############################################################################

eval $(/usr/local/bin/readhash /var/ipfire/ethernet/settings)
eval $(/usr/local/bin/readhash /var/ipfire/firewall/settings)
eval $(/usr/local/bin/readhash /var/ipfire/optionsfw/settings)

function iptables() {
	/sbin/iptables --wait "$@"
}

iptables -F POLICYFWD
iptables -F POLICYOUT
iptables -F POLICYIN

if [ -f "/var/ipfire/red/iface" ]; then
	IFACE="$(</var/ipfire/red/iface)"
fi

# Figure out what devices are configured.
HAVE_BLUE="false"
HAVE_ORANGE="false"

case "${CONFIG_TYPE}" in
	2)
		HAVE_ORANGE="true"
		;;
	3)
		HAVE_BLUE="true"
		;;
	4)
		HAVE_BLUE="true"
		HAVE_ORANGE="true"
		;;
esac

HAVE_IPSEC="true"
HAVE_OPENVPN="true"
HAVE_WG="true"

# INPUT

# Drop syslog from anywhere but localhost
# sysklogd cannot bind to specific interface and therefore we need to
# block access by adding firewall rules
case "${FWPOLICY}" in
	REJECT)
		iptables -A POLICYIN -p udp --dport 514 -j REJECT --reject-with icmp-host-unreachable
		;;
	*)
		iptables -A POLICYIN -p udp --dport 514 -j DROP
		;;
esac

# Allow access from GREEN
if [ -n "${GREEN_DEV}" ]; then
	iptables -A POLICYIN -i "${GREEN_DEV}" -j ACCEPT
fi

# Allow access from BLUE
if [ "${HAVE_BLUE}" = "true" ] && [ -n "${BLUE_DEV}" ]; then
	iptables -A POLICYIN -i "${BLUE_DEV}" -j ACCEPT
fi

# IPsec INPUT
case "${HAVE_IPSEC},${POLICY}" in
	true,MODE1) ;;
	true,*)
		iptables -A POLICYIN -m policy --pol ipsec --dir in -j ACCEPT
		;;
esac

# OpenVPN INPUT
# Allow direct access to the internal IP addresses of the firewall
# from remote subnets if forward policy is allowed.
case "${HAVE_OPENVPN},${POLICY}" in
	true,MODE1) ;;
	true,*)
		iptables -A POLICYIN -i tun+ -j ACCEPT
		;;
esac

# WireGuard INPUT
case "${HAVE_WG},${POLICY}" in
	true,MODE1) ;;
	true,*)
		iptables -A POLICYIN -i wg+ -j ACCEPT
		;;
esac

case "${FWPOLICY2}" in
	REJECT)
		if [ "${DROPINPUT}" = "on" ]; then
			iptables -A POLICYIN -m limit --limit 10/second -j LOG --log-prefix "REJECT_INPUT "
		fi
		iptables -A POLICYIN -j REJECT --reject-with icmp-host-unreachable -m comment --comment "DROP_INPUT"
		;;
	*) # DROP
		if [ "${DROPINPUT}" = "on" ]; then
			iptables -A POLICYIN -m limit --limit 10/second -j LOG --log-prefix "DROP_INPUT "
		fi
		iptables -A POLICYIN -j DROP -m comment --comment "DROP_INPUT"
		;;
esac

# FORWARD
case "${POLICY}" in
	MODE1)
		case "${FWPOLICY}" in
			REJECT)
				if [ "${DROPFORWARD}" = "on" ]; then
					iptables -A POLICYFWD -m limit --limit 10/second -j LOG --log-prefix "REJECT_FORWARD "
				fi
				iptables -A POLICYFWD -j REJECT --reject-with icmp-host-unreachable -m comment --comment "DROP_FORWARD"
				;;
			*) # DROP
				if [ "${DROPFORWARD}" = "on" ]; then
					iptables -A POLICYFWD -m limit --limit 10/second -j LOG --log-prefix "DROP_FORWARD "
				fi
				iptables -A POLICYFWD -j DROP -m comment --comment "DROP_FORWARD"
				;;
		esac
		;;

	*)
		# Access from GREEN is granted to everywhere
		if [ -n "${GREEN_DEV}" ]; then
			if [ "${IFACE}" = "${GREEN_DEV}" ]; then
				# internet via green
				# don't check source IP/NET if IFACE is GREEN
				iptables -A POLICYFWD -i "${GREEN_DEV}" -j ACCEPT
			else
				iptables -A POLICYFWD -i "${GREEN_DEV}" -s "${GREEN_NETADDRESS}/${GREEN_NETMASK}" -j ACCEPT
			fi
		fi

		# Grant access for IPsec VPN connections
		iptables -A POLICYFWD -m policy --pol ipsec --dir in -j ACCEPT

		# Grant access for OpenVPN connections
		iptables -A POLICYFWD -i tun+ -j ACCEPT

		# Grant access for WireGuard
		iptables -A POLICYFWD -i wg+ -j ACCEPT

		if [ -n "${IFACE}" ]; then
			if [ "${HAVE_BLUE}" = "true" ] && [ -n "${BLUE_DEV}" ]; then
				iptables -A POLICYFWD -i "${BLUE_DEV}" -s "${BLUE_NETADDRESS}/${BLUE_NETMASK}" -o "${IFACE}" -j ACCEPT
			fi

			if [ "${HAVE_ORANGE}" = "true" ] && [ -n "${ORANGE_DEV}" ]; then
				iptables -A POLICYFWD -i "${ORANGE_DEV}" -s "${ORANGE_NETADDRESS}/${ORANGE_NETMASK}" -o "${IFACE}" -j ACCEPT
			fi
		fi

		if [ "${DROPFORWARD}" = "on" ]; then
			iptables -A POLICYFWD -m limit --limit 10/second -j LOG --log-prefix "DROP_FORWARD "
		fi
		iptables -A POLICYFWD -m comment --comment "DROP_FORWARD" -j DROP
		;;
esac

# OUTGOING
case "${POLICY1}" in
	MODE1)
		case "${FWPOLICY1}" in
			REJECT)
				if [ "${DROPOUTGOING}" = "on" ]; then
					iptables -A POLICYOUT -m limit --limit 10/second -j LOG --log-prefix "REJECT_OUTPUT "
				fi
				iptables -A POLICYOUT -j REJECT --reject-with icmp-host-unreachable -m comment --comment "DROP_OUTPUT"
				;;
			*) # DROP
				if [ "${DROPOUTGOING}" == "on" ]; then
					iptables -A POLICYOUT -m limit --limit 10/second -j LOG --log-prefix "DROP_OUTPUT "
				fi
				iptables -A POLICYOUT -j DROP -m comment --comment "DROP_OUTPUT"
				;;
		esac
		;;
	*)
		iptables -A POLICYOUT -j ACCEPT
		iptables -A POLICYOUT -m comment --comment "DROP_OUTPUT" -j DROP
		;;
esac

exit 0

### 
# $Id: gnump3d.conf,v 1.23 2006/04/26 13:44:40 skx Exp $
###
#
#  This is the configuration file for GNUMP3d, the streaming MP3/OGG server.
#
#  All lines beginning with '#' are comments and may be ignored.
#
###
#
#  user = nobody  # Some comment
#
#  # The port to listen upon.
#  port=3333
#
###
#
#  If you believe the documentation here is incomplete or misleading 
# please consider contributing an update - or reporting it as a bug.
#
#  Comments on sensible defaults to ship this file with, or new options
# should be sent to the author.
#
###
#
#  Steve
#  ---
#  http://www.gnump3d.org/   - Website for this software, including 
#                              documentation and mailing lists.
#
#  http://www.steve.org.uk/  - My homepage.  Visit.  Enjoy.
#
####




###
#   Basic Server Settings
####

#
#  The port upon which we are going to serve.  This may be any
# port which is not already in use upon your machine.
#
#  You may use a low port, for example 80, if you are running
# as root - but that is only recommended if you make use of
# the `user' option, which occurs later in this file.
#
port = 8888


#
#  The following is the interface address that we wish to
# bind upon.
#
#  You'll only need set this if you are running this server upon
# a machine which has multiple IP addresses and you wish to force
# it to bind to a particular address.
#
#  If you're not running this server on a machine with multiple
# IP addresses, then please ignore this setting.
#
#
# NOTE
######
#
#  If you just want to specify the name used in all the generated
# links then you should be setting `hostname' instead.
#
# binding_host = ***********
#


#
#  Set the hostname that is used in all the generated links.
#
#  (This defaults to the name of the server machine, with no
# domain suffix).
#
#  You won't need this unless the machine which is serving on
# MUST be accessed via its fully qualified domain name - for
# example if you're running it on a machine which is accessible
# to the world.
#
# NOTE
######
#
#  See `binding_host' if you have a machine which has multiple
# IP addresses and you're trying to bind to one in particular.
#
# hostname = mp3d.foo.org
#


#
#  The root directory where we serve content from, this should be
# the directory containing the MP3, OGG, or other media files that
# you wish to share.
#
#  Obviously the files which you wish to serve should be accessible
# to the user who this server is running as - which is set via the
# 'user' setting below.
#
#  If you're on a multi-user system its probably a good idea to
# make the audio files readable to other users anyway, share the
# musical goodness!
#
root = /var/mp3


#
#  This is the file which logs all MP3's / OGG's / directories 
# which clients access.
#
#  If you start the server as root it's recommended that you use
# a canonical location such as '/var/log' or '/var/adm', (because
# your system will, probably, rotate files held there).
#
#  If you're starting this server as a non-root user you should 
# change these locations to be a directory upon which  you have write 
# permissions.
#
####
#
#  IMPORTANT NOTE:
#
#   If you want the song statistics to be displayed correctly
#  you MUST give the logfile as an absolute path - you must also
#  ensure that the logfile is readable to the user whom you are
#  running the server as.
#
####
#
#  NOTE:
#
#   This logfile is 100% apache logfile compatible so you may run any
#  Apache logfile processor upon it if you so wish.
#
#   Simplistic statistics such as the most popular directories, songs,
#  and clients are available via the installed script 'gnump3d-top'
#  Run 'gnump3d-top --help' for details.
#
#
####
#
logfile = /var/log/gnump3d/access.log


#
#  Specify the format of the logfile, this may use any valid variables
# which are defined in package "main".
#
#
log_format = $connected_address - $user [$date] "GET $REQUEST" $HTTP_CODE $SERVED_SIZE "-" "$USER_AGENT"



#
#  In the case of problems all output from the server will be sent
# to an error log.
# 
#  This next option configures where that is
#
errorlog = /var/log/gnump3d/error.log



#
#  This option allows you to specify additional arguments to the
# statistics generating program 'gnump3d-top', and it's complete
# path if you don't have it installed upon the $PATH of the user
# who starts the server:
#
# stats_program = /usr/bin/gnump3d-top
#
#
#  For example you may change the default number of lines which are
# reported from the default of 20 to 10 with this:
#
# stats_arguments = --count=10
#
#  To hide the directories which are handled by the various plugins
# you may use the '--hide' option.
#
# stats_arguments = --hide
#
#  To read multiple logfiles, (eg to cope with files which have been
# processed by logrotate), you could use:
#
# stats_arguments = --logfiles = /var/log/gnump3d/access.log*
#
#  Combine the options if you wish, and see the gnump3d-top man-page
# for details of more arguments
#
# stats_arguments = --count=40 --hide --logfiles=/var/log/gnump3d/access.log*
#




##
#  SECURITY OPTIONS.
####

#
#  If there is a user value setup below then the server will become that
# user, after creating the listening socket and after opening the logfiles
# for writing.
#
#  If you want to run this server via init.d, (which has the effect of
# starting the daemon as root), you should make sure you have this set
# to an appropriate value.
#  Otherwise you will have the server running as root, which is clearly
# not a good idea - even in the unlikely event that this application is
# 100% bug free.
#
#  You may comment the line out if you are running the daemon from your home
# directory, and you are the only user with read access to the server root.
# Although this is not recommended way of running the server.
#
#  For the benefit of fellow computer users is recommended that you run the
# server as a user such as 'nobody', and allow people read-only access to
# your audio files.
#
user = nobody




##
#  AUTHORISATION OPTIONS.
####

#
#  The following section allows you to specify which clients
# are allowed to connect to your server.
#
#  If you are leaving this server out on an Internet-accessible
# machine then you will probably want to setup something here,
# or at least make use of the password protection system.
#
#  The following entries are commented out, so they have no effect,
# but they should serve as good examples of the things which 
# are possible.
#
#
# NOTE
# ----
#
#  Multiple settings are allowed - just separated them with ';'
#
#
#  Only people on the same subnet, (class C):
# allowed_clients = ***********/8
#
#  People on the same class B:
# allowed_clients = 10.0.0.0/16
#
#  Only one person:
# allowed_clients = ************
#
#  Everybody
# allowed_clients = all
#
#  Nobody (!)
# allowed_clients = none
#
#  Everybody local, and one remote address:
# allowed_clients = ***********/8; *************
#
#  Everybody local, and one remote range:
# allowed_clients = ***********/8; ************/8
#
#
allowed_clients = all


#
#  'denied_clients' is the counterpart to allowed_clients - here
# you can specify particular clients who are unable to browse your
# music archive.
#
#  Usage is identical to the allowed clients above; eg:
#
#  denied_clients =  ************; ***********5; ***********/8
#
# denied_clients = none
#


##
#
#  This section controls whether we will prompt clients for
# a username and password prior to granting access to the 
# server.
#
#  This is enabled by default; all you need to do is create
# a password file in the MP3 directory called '.password'.
#
#  (To disable this uncomment the 'enable_password_protection' line).
#
#   The password file should be of the following format:
#
#  username:password
#  username2:password2
#  ...:....
#  usernameN:passwordN
#
#
#  NOTE
#  ----
#
#  The password file must be readable to the user the server is running
# as.
#
###
# enable_password_protection = 0
###


##
#
#  Referrer check
#
##
#
#  The last security option is the ability to block clients that don't
# contain a specific 'Referrer:' header.
#
#  Note that referrer protection isn't terribly reliable, as many clients
# will not send a referrer header when making a connection.  This is 
# especially likely when clients are connecting via a 'privacy enhancing'
# proxy server; such as Internet Junkbuster, etc.
#
#  Similarly malicious clients may deliberately stop sending referrers
# if they realise what is going on.
#
#
#
###
# If you wish to only allow a particular referring URL then specify it
# here.
# valid_referrers = http://somesite.com/


##
# SERVER FEATURES.
####

#
#  This next option controls what happens when you click upon a
# single MP3 file.
#
#  If always_stream is set to '1', and you click upon it you'll
# actually get a .m3u (playlist) file containing a link to it - 
# the advantage of doing this is that you don't have to download
# the file in its entirety to listen to it - it should start playing
# immediately.
#
#  If set to '0' the playlists will work via streaming, but selecting 
# an individual MP3 file will cause it to be downloaded to your local
# machine prior to playing - one drawback of this is that playback
# will probably not start until the entire file has downloaded.
#
always_stream = 1


#
#  There is a link placed at the top of every directory, which allows you
# to play the songs within that directory recursively.
#
#  By default the recursive option plays all the tracks in a
# sorted order - by uncommenting the following option you can
# have the recursive playlists default to being in a random order.
#
# recursive_randomize = 1
#

#
#  This next setting controls the format of the playslist files.
#
#  There are two formats this server supports, simple playlists which
# are just plain listings of tracks, and advanced playlists which contain
# copies of the song tags in comments.
#
#  The advanced playlists will allow you to view song tag information
# inside the playlist windows of both XMMS and WinAMP
#
#
advanced_playlists = 1
#



##
#  Server Appearance Settings.
####


##
#
#  All the output the server produces is generated with the help of
# some HTML template files, these are referred to throughout the
# program and each collection of templates is referred to collectively
# as a 'theme'.
#
#  This next option allows you to specify which theme should be used
# by default.  (Any client can select one of the available themes by
# the use of cookies).
#
#  For each theme there are a collection of HTML files which are read
# and used for the server output.  There are several basic files which
# must be supplied to be considered a theme:
#
#  index.html   -> Used for displaying individual directories.
#  search.html  -> Used for displaying the search form.
#  results.html -> Used for displaying the results of a search.
#  stats.html   -> Used for customising the server statistics page.
#  
#  In addition to those each plugin reads in it's own template file
# and uses that.  For example the statistics plugin is accessed via
# the URL   http://server.host.name:port/stats/, and reads in a template
# called 'stats.html'
#
#  The theme directory defaults to /usr/share/gnump3d - but may be
# changed via the 'theme_directory' setting given below.
#
#  When the server is running you may set the theme from your browser
# via the preferences page, simply visit the link:
#
#      http://server.host.name:port/prefs
#
#
#  NOTE
#  ----
#
#   If you create a theme I'd be happy to include it in future releases
#  if you wish - see the online theme creation docs at:
#
#	http://www.gnump3d.org/theme.html
#
theme = Liquid



##
#
#  Set the base theme directory if you wish to place them in a non-standard
# location.
#
##
theme_directory = /usr/share/gnump3d/
##



##
#
#  This next option controls how directories are displayed on the index
# pages. 
#
#  There are several defined variables which you can choose to use:
#
#  $LINK       = The actual URL of the directory.
#
#  $DIR_NAME   = The name of the directory.
#
#  $SONG_COUNT = The number of songs contained in the directory.
#
#  $DIR_COUNT  = The number of subdirectories in the directory.
#
#  $RECURSE    = A link to play the contents of the directory recursively.
#
#  $NEW        = Will insert the text specified by 'new_format' if the
#                directory is recently modified.
#
#  NOTE: you can also specify "directory_format2" as an alternative display
#  format. Directories are then listed with alternating display formats.
#  This is useful if you want to have alternating background colors for
#  each entry in a table, for example.
#
directory_format = <tr><td width="10%">&nbsp;</td><td><a href="$LINK">$DIR_NAME</a> $NEW</td><td>$SONG_COUNT</td><td>$DIR_COUNT</td><td>[$RECURSE]</td></tr></a>

#
# directory_format = <tr><td><a href="$LINK">$DIR_NAME</a></td></tr>
#
# directory_format = <tr><td><a href="$LINK">$DIR_NAME</a></td></tr>
#
# directory_format = <tr><td><a href="$LINK">$DIR_NAME</a></td><td>[$RECURSE]</td></tr>
#


##
#  Text to insert if the directory is recently modified.
##
new_format = <font color="red"><b>New</b></font>

##
#  If a directory hasn't been modifed in more than 'x' days then
# it isn't considered 'new'.
##
new_days   = 14


##
# File format
##
#
#   This option is analogous to the 'directory_format' option above.
#
#   It specifies how the files should be listed.
#
#   $LINK         Is the link to the track itself
#
#   $SONG_FORMAT  Is a template string which is described below.
#
#  NOTE: you can also specify "file_format2" as an alternative display
#  format. Files are then listed with alternating display formats.
#  This is useful if you want to have alternating background colors for
#  each entry in a table, for example.
#
file_format = <tr><td width="10%">&nbsp;</td><td><a href="$LINK">$SONG_FORMAT</a></td><td align="right">[<a href="/info$PLAINLINK">Info</a>] [<a href="$PLAINLINK">Download</a>]</td></tr>



##
#
#  This next setting, `song_format', allows you to configure how the
# MP3 and OGG Vorbis files are displayed.
#
#  If the file which has been found contains any tag information then
# the following values will be available to you:
#
#   $ARTIST   -> The artist of the song.
#
#   $ALBUM    -> The album the song came from.
#
#   $COMMENT  -> The comment string attached to the song, if any.
#                NOT AVAILABLE FOR OGG FILES.
#
#   $SONGNAME -> The name of the song.
#
#   $YEAR     -> The year the song was recorded.
#                NOT AVAILABLE FOR OGG FILES.
#
#   $GENRE    -> The genre of the track.
#                NOT AVAILABLE FOR OGG FILES.
#
#   $BPS      -> The sample rate of the song - if non-variable.
#                NOT AVAILABLE FOR OGG FILES.
#
#   $LENGTH   -> The length of the track in MM:SS
#                NOT AVAILABLE FOR OGG FILES, OR VBR MP3's.
#
#   $SECONDS  -> The length of the track in seconds
#                NOT AVAILABLE FOR OGG FILES, OR VBR MP3's.	         
#
#   $SIZE     -> The size of the audio stream.
#                NOT AVAILABLE FOR OGG FILES, OR VBR MP3's.
#
#   $TRACK    -> The number of the track
#
#   $FILENAME will always be available.  (This is the name of the file
# without any suffix or directory information).
#
song_format = $TRACK - $ARTIST - $ALBUM - $SONGNAME [ $GENRE - $LENGTH / $SIZE ] $NEW



##
#
#  If you wish to change the text displayed on the 'Play'
# link you should uncomment the following option, and change the
# text appropriately.
#
##
#
# play_recursively_text = Play
##


##
#
#  This next option controls how songs are sorted prior to display,
# and used in the playlists.
# 
#  You may use any of the $ values which are listed above in 
# the 'song_format' setting, eg:
#
# Sort by the song name.
# sort_order = $SONGNAME
#
# Sort by artist if present.
# sort_order = $ARTIST
##
# Default to sorting by the track number, due to popular.
sort_order = $TRACK





##
# DOWNSAMPLING / BIT RATE CHANGE
##
#
#  This support allows the music to be down-sampled before it is streamed
# to clients.  This will slow down the serving, and may be a little
# processor intensive - I guess it will depend upon the speed of your
# server, and the number of users you have.
#
##
#
#  Is downsampling support enabled?
#
#  If this is disabled then none of the further downsampling options will
# apply, even if they are set.
#
#  Also note that if the downsampling support is disabled the preferences
# page will not allow the clients to select their own bitrate.
#
#  (I think this makes sense, if it's disabled it's misleading to present
# configuration options to the clients).
# 
# downsample_enabled = 1
#

##
#
# YOU MUST UNCOMMENT THESE LINES
#
#  Downsampling is based upon IP address, rather than enabled globally
# this allows you to control things a little more tightly than user
# preferences.
#
#  For example on a gateway machine you'd want all people on the internal
# LAN to have full quality files, and people remote to have the lower
# quality sounds.
#
#  The next two settings specify the downsampling ranges:
#
# downsample_clients = ALL
# no_downsample_clients = ***********/24
#
#  NOTE:  'no_downsample_clients' takes precedence over 'downsample_clients'.
#
#  Here are some examples, notice that you can use either single IP
# addresses, ranges, 'NONE' or 'ALL'
#
#
#   Downsample Everybody:
#       downsample_clients = ALL
#       no_downsample_clients = NONE
#
#   Downsample remote, allow local to have full quality
#       downsample_clients = ALL
#       no_downsample_clients = ***********/24
#
#   Downsample all except two addresses
#       downsample_clients = ALL
#       no_downsample_clients = *************; ************
#
#   Full quality for all local, except one address
#       downsample_clients = ************
#       no_downsample_clients = ***********/24
#
##

##
#
# YOU MUST UNCOMMENT THESE LINES
#
#
#  For each of the downsampling levels the user has chosen the selected
# file will be processed by a command.
#
#  This command is assumed to write it's output directly to STDOUT.
#
#  The following options specify the command line to run, note that 
# the string '$FILENAME' will be replaced by the file the user is
# requesting.
#
# Note:
#
#  If you wish to downsample or convert any other file types you
# need to add entries to this file to specify what is done to them
# simply create an entry which looks like:
#
#   downsample_"level"_"file suffix" = command args
#
#downsample_high_mp3   = /usr/bin/lame  --mp3input -b 56 $FILENAME -
#downsample_medium_mp3 = /usr/bin/lame  --mp3input -b 32 $FILENAME -
#downsample_low_mp3    = /usr/bin/lame  --mp3input -b 16 $FILENAME -
#
#downsample_high_ogg   = /usr/bin/sox -t ogg $FILENAME -t raw - | oggenc --raw --downmix -b 96 -
#downsample_medium_ogg = /usr/bin/sox -t ogg $FILENAME -t raw - | oggenc --raw --downmix -b 64 -
#downsample_low_ogg    = /usr/bin/sox -t ogg $FILENAME -t raw - | oggenc --raw --downmix --resample 22050 -b 32 -
#

#
#  If you always want to force a particular bitrate upon clients who've
# not chosen something different then specify it here:
#
# default_quality = medium

#
#
##





####
#  Plugin control
##

#
#  Several of the servers features are controlled via external plugins,
# these are simple Perl scripts which are called by the server in response
# to requests.
#
#  This next setting allows you to change the location from which the
# plugins are loaded.
#
# plugin_directory = /usr/lib/perl5/gnump3d/plugins
#
#
#  NOTE: the following directory should be correctly filled in by
# the installation script - if this doesn't happen report this as
# a bug.
#
plugin_directory = /usr/lib/perl5/5.36.0/gnump3d/plugins
#


#
#  Plugin-Specific settings here.
#


#
#  Directories to exclude when choosing a random directory.  Seperate
# multiple directories with a '|' character.
#
# plugin_random_exclude = Talk|Midi|Video

#
##
#  End Plugin Control
####



##
#  HACKING - atypical configuration follows, this will not be of 
#            interest to casual users.
#
#  Dragons lie yonder ... ->
#
#####

#
#  When files are served there needs to be an appropriate mime type
# sent along with them, this allows the browser to pass the response
# back to an MP3 player, etc.
#
#  Most Unix systems will have the file '/etc/mime.types' which is
# used by default.  If not point this next file to wherever your mime
# types file is located:
#
mime_file = /etc/gnump3d/mime.types
#
#

#
#  This next setting controls where the file types are looked up.
# we support many audio, visual, and playlist files and to do this
# we need to be able to lookup a file to determine what kind of
# file it is.
#
#  Here is the lookup file.
file_types = /etc/gnump3d/file.types

#
#
#  In order to record which songs are currently being played the server
# will create a temporary file containing that path of each file that
# it is serving.
#
#  The path under which these files are created in may be set here.
#
now_playing_path = /var/cache/gnump3d/serving


#
#  As a speedup and optimisation song tag is read via a companion script
# which is called 'gnump3d-index'.  This will be run automatically when
# the server starts if possible.
#
#  This next setting controls where that script will write its tag database
# to.
#
tag_cache = /var/cache/gnump3d/song.tags

#
#  This next setting allows you to control the path to the gnump3d-index
# script which is used, as explained above.
#
#  Normally you shouldn't need to touch this.
#
# index_program = /usr/bin/gnump3d-index


#
#  This setting controls whether we should stream song title
# information in shoutcast format.
#
#  If your client supports it then you should enable it, otherwise
# not.
#
#  (This works correctly on WinAmp, and XMMS.)
#
shoutcast_streaming = 1


#
#  By default the server logfile is appended to when we start.
#
#  If you set the following value to '1' the logfile will be truncated
# instead.
#
#  You might want to truncate the file to save space, but doing this
# renders the server statistics less useful.
#
# truncate_log_file = 0


#
#  By default the server will close a connection it receives if it
# hasn't received a valid request after 10 seconds.
#
#  Here you may change this timeout value.
#
#  read_time = 10



#
#  This next section controls whether we present a HTML interface
# to the server.
#
#  In most situations you will want to do this - but some people have
# approached me and suggested that it should be tweakable, so here's
# the option to disable the browsing of the music
#
#  If you set this to '0' the best you can probably do is call
# 'http://server:port/recurse.m3u' - or similar.
#
# enable_browsing = 1


#
#  This next value controls whether we should believe, and use, the
# client supplied 'Host:' header.
#
#  This header is sent by HTTP/1.1 clients only, and could be spoofed.
# You'll only NEED to use it if tunnelling over SSH or performing similar
# magic.
#
#  It's enabled here because I don't believe it should cause any real harm
# YMMV.
#
use_client_host = 1


##
#
# Set this option to rewrite the hostname:port combination in the URLs in
# your playlist.  Most people won't need this unless they are proxying
# their connection or running multiple streaming servers through a load
# balancer or DNS rotor.
#
# Note: If you set this you will almost certainly need to set
#       "use_client_host=0"
##
# host_rewrite = ""


##
#
#  This is an experimental feature - ignore it.
#
#  It does not work.
#
#  Ignore it: You are not expected to understand this (I've always wanted
# to leave that as a comment somewhere ;)
#
##
#
#  jukebox_mode   = 1
#  jukebox_player = /usr/bin/mpg123 $FILENAME
#
##


##
#
#  Make it possible to hide song tags, and just display filenames.
#
##
# hide_song_tags = 0

##
#
#  Make it possible to disable the tag cache altogether, to save memory.
#
##
# disable_tag_cache = 0


##
#
#  This option allows you to insert a custom META-tag inside the header of
# each page which is output.
#
#  You can add whatever you like here, perhaps the most useful thing to
# add is an encoding type.
#
# add_meta_tag = <meta name="author" value="Steve Kemp" />
# add_meta_tag = <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
##


##
#   End Of Hacking Section
####



##
#  End of gnump3d.conf
####

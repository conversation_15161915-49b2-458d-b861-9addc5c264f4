#usr/bin/cargo
#usr/bin/rust-gdb
#usr/bin/rust-gdbgui
#usr/bin/rust-lldb
#usr/bin/rustc
#usr/bin/rustdoc
#usr/etc/bash_completion.d
#usr/etc/bash_completion.d/cargo
#usr/lib/libchalk_derive-73a40a6a15aa6119.so
#usr/lib/libcstr-435995457a32217b.so
#usr/lib/libderive_more-89bd279f3334d3f2.so
#usr/lib/libdisplaydoc-ccaea0b1dd937724.so
#usr/lib/libicu_provider_macros-bdd161fb91bfac75.so
#usr/lib/libproc_macro_hack-5515f5e9de7f3b33.so
#usr/lib/librustc_driver-c21064f0d6190bc9.so
#usr/lib/librustc_macros-9459ff5fdab1896f.so
#usr/lib/libserde_derive-9ec19eb421babc4f.so
#usr/lib/libstd-f6fab35afe949852.so
#usr/lib/libtest-15e26e6e92c6d6a9.so
#usr/lib/libthiserror_impl-d8ffaa228f9b0a09.so
#usr/lib/libtracing_attributes-babc061b68390947.so
#usr/lib/libunic_langid_macros_impl-25424350b834739e.so
#usr/lib/libyoke_derive-ad9526c2c89e2145.so
#usr/lib/libzerofrom_derive-ef5640f90c8390cf.so
#usr/lib/libzerovec_derive-6b7809d259a5aea6.so
#usr/lib/rustlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin/gcc-ld
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin/gcc-ld/ld.lld
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin/gcc-ld/ld64.lld
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin/gcc-ld/lld-link
#usr/lib/rustlib/aarch64-unknown-linux-gnu/bin/gcc-ld/wasm-ld
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libaddr2line-214015861667ba9c.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libadler-d3734ff9791968ba.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/liballoc-a9cb98efa50102fe.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libcfg_if-d76500918c8766e9.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libcompiler_builtins-0222020c3eea44f8.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libcore-ded8ec8026a81d22.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libgetopts-f27c9689662442e2.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libgimli-7d5d0c1e1e66cafd.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libhashbrown-db473b4fe6ee2788.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/liblibc-c889f940e97ce094.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libmemchr-5a2e11949e6321e8.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libminiz_oxide-1dd46390c6037e19.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libobject-bcf3087a1126b654.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libpanic_abort-5843d82f1254d4af.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libpanic_unwind-6f5a332029a62322.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libproc_macro-e301cc22fe5c6c1c.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libprofiler_builtins-2b2e90d3a4db58a2.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc-stable_rt.asan.a
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc-stable_rt.hwasan.a
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc-stable_rt.lsan.a
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc-stable_rt.msan.a
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc-stable_rt.tsan.a
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc_demangle-b11737a5f240b7f8.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc_std_workspace_alloc-83bbc9721056031d.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc_std_workspace_core-9ae314eb5ffdbb16.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/librustc_std_workspace_std-f6be17bf7bedad3b.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libstd-f6fab35afe949852.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libstd-f6fab35afe949852.so
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libstd_detect-f68e9d9079b1320e.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libtest-15e26e6e92c6d6a9.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libtest-15e26e6e92c6d6a9.so
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libunicode_width-0d0e75e55a2615d2.rlib
#usr/lib/rustlib/aarch64-unknown-linux-gnu/lib/libunwind-2a36edb432000403.rlib
#usr/lib/rustlib/components
#usr/lib/rustlib/etc
#usr/lib/rustlib/etc/gdb_load_rust_pretty_printers.py
#usr/lib/rustlib/etc/gdb_lookup.py
#usr/lib/rustlib/etc/gdb_providers.py
#usr/lib/rustlib/etc/lldb_commands
#usr/lib/rustlib/etc/lldb_lookup.py
#usr/lib/rustlib/etc/lldb_providers.py
#usr/lib/rustlib/etc/rust_types.py
#usr/lib/rustlib/install.log
#usr/lib/rustlib/manifest-cargo
#usr/lib/rustlib/manifest-rust-std-aarch64-unknown-linux-gnu
#usr/lib/rustlib/manifest-rustc
#usr/lib/rustlib/rust-installer-version
#usr/lib/rustlib/uninstall.sh
#usr/libexec/cargo-credential-1password
#usr/libexec/rust-analyzer-proc-macro-srv
#usr/share/cargo
#usr/share/cargo/registry
#usr/share/doc/cargo
#usr/share/doc/cargo/LICENSE-APACHE
#usr/share/doc/cargo/LICENSE-MIT
#usr/share/doc/cargo/LICENSE-THIRD-PARTY
#usr/share/doc/cargo/README.md
#usr/share/doc/rust
#usr/share/doc/rust/COPYRIGHT
#usr/share/doc/rust/LICENSE-APACHE
#usr/share/doc/rust/LICENSE-MIT
#usr/share/doc/rust/README.md
#usr/share/man/man1/cargo-add.1
#usr/share/man/man1/cargo-bench.1
#usr/share/man/man1/cargo-build.1
#usr/share/man/man1/cargo-check.1
#usr/share/man/man1/cargo-clean.1
#usr/share/man/man1/cargo-doc.1
#usr/share/man/man1/cargo-fetch.1
#usr/share/man/man1/cargo-fix.1
#usr/share/man/man1/cargo-generate-lockfile.1
#usr/share/man/man1/cargo-help.1
#usr/share/man/man1/cargo-init.1
#usr/share/man/man1/cargo-install.1
#usr/share/man/man1/cargo-locate-project.1
#usr/share/man/man1/cargo-login.1
#usr/share/man/man1/cargo-metadata.1
#usr/share/man/man1/cargo-new.1
#usr/share/man/man1/cargo-owner.1
#usr/share/man/man1/cargo-package.1
#usr/share/man/man1/cargo-pkgid.1
#usr/share/man/man1/cargo-publish.1
#usr/share/man/man1/cargo-remove.1
#usr/share/man/man1/cargo-report.1
#usr/share/man/man1/cargo-run.1
#usr/share/man/man1/cargo-rustc.1
#usr/share/man/man1/cargo-rustdoc.1
#usr/share/man/man1/cargo-search.1
#usr/share/man/man1/cargo-test.1
#usr/share/man/man1/cargo-tree.1
#usr/share/man/man1/cargo-uninstall.1
#usr/share/man/man1/cargo-update.1
#usr/share/man/man1/cargo-vendor.1
#usr/share/man/man1/cargo-verify-project.1
#usr/share/man/man1/cargo-version.1
#usr/share/man/man1/cargo-yank.1
#usr/share/man/man1/cargo.1
#usr/share/man/man1/rustc.1
#usr/share/man/man1/rustdoc.1
#usr/share/zsh
#usr/share/zsh/site-functions
#usr/share/zsh/site-functions/_cargo

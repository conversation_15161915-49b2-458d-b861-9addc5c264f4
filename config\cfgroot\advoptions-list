option all-subnets-local flag;
option arp-cache-timeout uint32;
option bootfile-name string;
option boot-size uint16;
option broadcast-address ip-address;
option cookie-servers ip-address [, ip-address... ];
option default-ip-ttl uint8;
option default-tcp-ttl uint8;
option dhcp-client-identifier data-string;
option domain-search domain-list;
option finger-server ip-address [, ip-address... ];
option font-servers ip-address [, ip-address... ];
option host-name string;
option ieee802-3-encapsulation flag;
option ien116-name-servers ip-address [, ip-address... ];
option impress-servers ip-address [, ip-address... ];
option interface-mtu uint16;
option ip-forwarding flag;
option irc-server ip-address [, ip-address... ];
option log-servers ip-address [, ip-address... ];
option lpr-servers ip-address [, ip-address... ];
option mask-supplier flag;
option max-dgram-reassembly uint16;
option merit-dump string;
option mobile-ip-home-agent ip-address [, ip-address... ];
option ms-classless-static-routes string;
option netbios-dd-server ip-address [, ip-address... ];
option netbios-node-type uint8;
option netbios-scope string;
option nis-domain string;
option nisplus-domain string;
option nisplus-servers ip-address [, ip-address... ];
option nis-servers ip-address [, ip-address... ];
option nntp-server ip-address [, ip-address... ];
option non-local-source-routing flag;
option ntp-servers ip-address [, ip-address... ];
option path-mtu-aging-timeout uint32;
option path-mtu-plateau-table uint16 [, uint16... ];
option perform-mask-discovery flag;
option policy-filter ip-address ip-address [, ip-address ip-address... ];
option pop-server ip-address [, ip-address... ];
option resource-location-servers ip-address [, ip-address... ];
option rfc3442-classless-static-routes string;
option root-path string;
option router-discovery flag;
option router-solicitation-address ip-address;
option smtp-server ip-address [, ip-address... ];
option static-routes ip-address ip-address [, ip-address ip-address... ];
option streettalk-directory-assistance-server ip-address [, ip-address... ];
option streettalk-server ip-address [, ip-address... ];
option swap-server ip-address;
option tcp-keepalive-garbage flag;
option tcp-keepalive-interval uint32;
option tftp-server-name string;
option time-offset int32;
option time-servers ip-address [, ip-address... ];
option trailer-encapsulation flag;
option www-server ip-address [, ip-address... ];
option x-display-manager ip-address [, ip-address... ];

net.ipv4.ip_forward = 1
net.ipv4.ip_dynaddr = 1

net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.icmp_ratelimit = 1000
net.ipv4.icmp_ratemask = 6168

net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_syn_retries = 3
net.ipv4.tcp_synack_retries = 3

net.ipv4.conf.default.arp_filter = 1
net.ipv4.conf.default.rp_filter = 1
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.default.log_martians = 1

net.ipv4.conf.all.arp_filter = 1
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1

kernel.printk = 1 4 1 7
vm.mmap_min_addr = 4096
vm.min_free_kbytes = 8192

# Disable IPv6 by default.
net.ipv6.conf.all.disable_ipv6 = 1
net.ipv6.conf.default.disable_ipv6 = 1

# However, enable some IPv6 hardening sysctl's in case this system is run customly _with_ IPv6.
net.ipv6.conf.all.accept_redirects = 0
net.ipv6.conf.default.accept_redirects = 0

# Enable netfilter accounting
net.netfilter.nf_conntrack_acct = 1

# Disable netfilter on bridges.
net.bridge.bridge-nf-call-ip6tables = 0
net.bridge.bridge-nf-call-iptables = 0
net.bridge.bridge-nf-call-arptables = 0

# Restrict loading TTY line disciplines to CAP_SYS_MODULE to prevent unprivileged attackers
# from loading vulnerable line disciplines with the TIOCSETD ioctl.
dev.tty.ldisc_autoload = 0

# Try to keep kernel address exposures out of various /proc files (kallsyms, modules, etc).
kernel.kptr_restrict = 2

# Avoid kernel memory address exposures via dmesg.
kernel.dmesg_restrict = 1

# Turn on hard- and symlink protection
fs.protected_symlinks = 1
fs.protected_hardlinks = 1

# Don't allow writes to files and FIFOs that we don't own in world writable sticky
# directories, unless they are owned by the owner of the directory.
fs.protected_fifos = 2
fs.protected_regular = 2

# If a workload mostly uses anonymous memory and it hits this limit, the entire
# working set is buffered for I/O, and any more write buffering would require
# swapping, so it's time to throttle writes until I/O can catch up.  Workloads
# that mostly use file mappings may be able to use even higher values.
#
# The generator of dirty data starts writeback at this percentage (system default
# is 20%)
vm.dirty_ratio = 10

# Start background writeback (via writeback threads) at this percentage (system
# default is 10%)
vm.dirty_background_ratio = 3

# The swappiness parameter controls the tendency of the kernel to move
# processes out of physical memory and onto the swap disk.
# 0 tells the kernel to avoid swapping processes out of physical memory
# for as long as possible
# 100 tells the kernel to aggressively swap processes out of physical memory
# and move them to swap cache
vm.swappiness = 1

# Increase kernel buffer size maximums
net.ipv4.tcp_mem = 16777216 16777216 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 16384 16777216
net.ipv4.udp_mem = 3145728 4194304 16777216

# Prefer low latency over higher throughput
net.ipv4.tcp_low_latency = 1

# Reserve more socket space for the TCP window
net.ipv4.tcp_adv_win_scale = 2

# Enable TCP fast-open
net.ipv4.tcp_fastopen = 3

# Drop RST packets for sockets in TIME-WAIT state, as described in RFC 1337.
# This protects against various TCP attacks, such as DoS against or injection
# of arbitrary segments into prematurely closed connections.
net.ipv4.tcp_rfc1337 = 1

# Include PID in file names of generated core dumps
kernel.core_uses_pid = 1

# Block non-uid-0 profiling
kernel.perf_event_paranoid = 3

# Only processes with CAP_SYS_PTRACE may use ptrace
kernel.yama.ptrace_scope = 2

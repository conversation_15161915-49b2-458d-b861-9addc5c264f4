# LCDproc client configuration file

## general options ##
[lcdproc]
# address of the LCDd server to connect to
Server=localhost

# Port of the server to connect to
Port=13666

# set reporting level
ReportLevel=2

# report to to syslog ?
ReportToSyslog=false

# run in foreground [default: false; legal: true, false]
#Foreground=true

# PidFile location when running as daemon [default: /var/run/lcdproc.pid]
#PidFile=/var/run/lcdproc.pid

# slow down initial announcement of modes (in 1/100s)
#delay=2

# display name for the main menu [default: LCDproc HOST]
#DisplayName=lcdproc

## screen specific configuration options ##

[CPU]
# Show screen
Active=True
OnTime=1
OffTime=2
ShowInvisible=false

[Iface]
# Show screen
Active=True

# Show stats for Interface0
Interface0=green0
# Interface alias name to display [default: <interface name>]
Alias0=LAN

# Show stats for Interface1
Interface1=red0
Alias1=WAN

# Show stats for Interface2
Interface2=blue0
Alias2=WIFI

# for more than 3 interfaces change MAX_INTERFACES in iface.h and rebuild

# Units to display [default: byte; legal: byte, bit, packet]
unit=bit

# add screen with transferred traffic
#transfer=TRUE

[Memory]
# Show screen
Active=True

[Load]
# Show screen
Active=True
# Min Load Avg at which the backlight will be turned off [default: 0.05]
LowLoad=0.00
# Max Load Avg at which the backlight will start blinking [default: 1.3]
HighLoad=2.5

[TimeDate]
# Show screen
Active=True
# time format [default: %H:%M:%S; legal: see strftime(3)]
TimeFormat="%H:%M:%S"
# date format [default: %x; legal: see strftime(3)]
DateFormat="%x"

[About]
# Show screen
Active=false

[SMP-CPU]
# Show screen
Active=false

[OldTime]
# Show screen
Active=false
# time format [default: %H:%M:%S; legal: see strftime(3)]
TimeFormat="%H:%M:%S"
# date format [default: %x; legal: see strftime(3)]
DateFormat="%x"

[BigClock]
# Show screen
Active=false

[Uptime]
# Show screen
Active=false

[Battery]
# Show screen
Active=false

[CPUGraph]
# Show screen
Active=false

[ProcSize]
# Show screen
Active=false

[Disk]
# Show screen
Active=false

[MiniClock]
# Show screen
Active=True
# time format [default: %H:%M; legal: see strftime(3)]
TimeFormat="%H:%M"

# EOF

# MPD CONFIG FILE
# For a full description of all config parameters,
# Check the mpd man page, "man mpd".

##################### REQUIRED ###########################
port "6600"
music_directory "/var/mp3"
playlist_directory "/var/ipfire/mpfire"
log_file "/var/log/mpd.log"
pid_file "/var/run/mpd.pid"
##########################################################

##########################################################
# EVERYTHING ELSE IS OPTIONAL
##########################################################

#################### OPTIONAL FILES ######################
#
# Location of DB file 
#
db_file		"/var/ipfire/mpfire/db/mpd.db"
#
# The state file (if set) will be a file
# for storing all current information 
# (playlist, playing/paused, etc...) from
# the last MPD session. This will be used
# to recreate your last MPD session after
# restart.
#
state_file "/var/ipfire/mpfire/mpd_state"
#
##########################################################

################## AUDIO OUTPUT ##########################
#
audio_output {
        type                    "alsa"
        name                    "alsa"
#        device                  "hw:0,0"     # optional
#        format                  "44100:16:2" # optional
}
#
# An example of an OSS output:
#
#audio_output {
#        type                    "oss"
#        name                    "My OSS Device"
#        device                  "/dev/dsp"   # optional
#        format                  "44100:16:2" # optional
#}
#
# An example of a shout output (for streaming to Icecast):
#
#audio_output {
#        type                    "shout"
#        name                    "My Shout Stream"
#        host                    "localhost"
#        port                    "8000"
#        mount                   "/mpd.ogg"
#        password                "hackme"
#        quality                 "5.0"
#        bitrate                 "128"
#        format                  "44100:16:1"
#        user                    "source"                # optional
#        description             "My Stream Description" # optional
#        genre                   "jazz"                  # optional
#        public                  "no"                    # optional
#}
#
# Set this if you have problems 
# playing audio files.
# This will resample your music so
# that it comes out at the set rate. 
#
#audio_output_format	"44100:16:2"
#
# You should not need mess with 
# this value unless you know 
# what you're doing.
#
#audio_write_size	"1024"
#
##########################################################

################# REPLAYGAIN #############################
#
# Use Replay Gain (album or title)
#	http://www.replaygain.org
#
#replaygain		"album"
#
# Sets the pre-amp used for files that have replaygain
# info.  Valid values are between -15 to 15 (in dB).
#
#replaygain_preamp	"0"
#
##########################################################


################ OUTPUT BUFFER SETTINGS ##################
#
# You should not need to mess with this
# unless you know what you're doing.
#
audio_buffer_size	"2048"
#
# This means exactly what it says, it will
# buffer your file up to the percentage of
# the buffer before it begins playing.
#
buffer_before_play 	"25%"
#
##########################################################


################### HTTP PROXY ###########################
#
# http_proxy_host	"proxy.isp.com"
# http_proxy_port	"8080"
# http_proxy_user	"user"
# http_proxy_password	"password"
#
##########################################################


################# SECURITY SETTINGS ######################
#
# It is encouraged to run MPD as 
# non-superuser.  If you start mpd as root
# (for example, in an init script), set
# this value, then  mpd will drop root priveleges
# and runs as the user specified.
#
#user "nobody"
#
# Set this value if you only have one 
# address you want to allow connection to. 
#
bind_to_address "localhost"
#
# If you want to setup MPD to use 
# passwords set them here
#
#password 		"password1@read,add,control,admin"
#password 		"password2@read"
#
# Specify permissions used by default when no password is 
# given by for a connection/client.
#
#default_permissions	"read,add,control,admin"
#
##########################################


################ MISCELLANEOUS OPTIONS ###################
#
# This setting exists as precaution against attacks.
#
#max_playlist_length 	"16384"
#
# Valid options are "default", "secure" or "verbose".
#log_level		"default"
#
connection_timeout 	"60"
#
# This should be fine for 2-3 people using clients 
# at the same time.
#
#max_connections 	"5"
#
# No need to change these unless you know better.
#
#max_command_list_size 	"2048"
#max_output_buffer_size "2048"
#
# This will make playlists compatible with normal music
# players.
#
#save_absolute_paths_in_playlists "no"
#
##########################################################

###################### CHARACTER ENCODINGS #####################
#
# If file or directory names do not display correctly, then you
# may need to change this.  In most cases it should be either
# "ISO-8859-1" or "UTF-8".  You must recreate your database
# after changing this (use mpd --create-db).
#
filesystem_charset              "UTF-8"
#
# The encoding that ID3v1 tags should be converted from.
#
id3v1_encoding                  "UTF-8"
metadata_to_use "artist,album,title,track,name,comment,date,genre"
#
################################################################

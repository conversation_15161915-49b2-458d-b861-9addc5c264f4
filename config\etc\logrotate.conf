# rotate log files weekly
weekly

# keep 52 weeks worth of backlogs
rotate 52

# create new (empty) log files after rotating old ones
create

# uncomment this if you want your log files compressed
compress

# packages drop log rotation information into this directory
include /etc/logrotate.d

# wtmp
/var/log/wtmp {
    monthly
    create 0664 root utmp
    rotate 12
}

/var/log/httpd/access_log /var/log/httpd/error_log /var/log/httpd/ssl_request_log /var/log/httpd/ssl_engine_log {
    missingok
    sharedscripts
    postrotate
	/bin/kill -HUP `cat /var/run/httpd.pid 2>/dev/null` 2> /dev/null || true
    endscript
}

/var/log/suricata/*.log {
    weekly
    copytruncate
    compress
    ifempty
    missingok
    postrotate
	/bin/find /var/log/suricata -path '/var/log/suricata/[0-9]*' -prune -exec /bin/rm -rf {} \;
	/bin/find /var/log/suricata -name 'fast.log.*' -mtime +28 -exec /bin/rm -rf {} \;
	/bin/kill -HUP `cat /var/run/suricata.pid 2> /dev/null` 2> /dev/null || true
    endscript
}

/var/log/squid/access.log /var/log/squid/user_agent.log /var/log/squid/referer.log {
    weekly
    copytruncate
    ifempty
    missingok
}

/var/log/squid/cache.log {
    weekly
    rotate 3
    copytruncate
    compress
    missingok
}

/var/log/squid/store.log {
    weekly
    rotate 3
    copytruncate
    compress
    missingok
    postrotate
	/bin/chmod -R ugo+rX /var/log/squid
	/usr/sbin/squid -k rotate
    endscript
}

/var/log/messages /var/log/bootlog /var/log/dhcpcd.log /var/log/mail {
    create 664 root syslogd
    sharedscripts
    ifempty
    postrotate
	/bin/kill -HUP `cat /var/run/syslogd.pid 2> /dev/null` 2> /dev/null || true
    endscript
}

/var/log/squidGuard/*.log {
    weekly
    rotate 4
    copytruncate
    compress
    notifempty
    missingok
}

/var/log/updatexlrator/*.log {
    weekly
    rotate 4
    copytruncate
    compress
    notifempty
    missingok
}

!
! Pmacctd configuration file for IPFire environment
!

!-----------------------------------  global  -----------------------------------

syslog: daemon
daemonize: true
debug: false
promisc: true
pcap_interface: green0

imt_mem_pools_number: 256

plugins: memory[plugin1]		#	, sqlite3[plugin2]


!-----------------------------------  memory  -----------------------------------

!
! "plugin1" plugin configuration
!

plugin_buffer_size[plugin1]: 102400
plugin_pipe_size[plugin1]:   10240000

imt_path[plugin1]: /var/spool/pmacct/plugin1.pipe

aggregate[plugin1]: src_host, src_port, src_mac, dst_host, dst_port, dst_mac, proto
aggregate_filter[plugin1]: ip


!-----------------------------------  sqlite3  ----------------------------------

!
! "plugin2" plugin configuration
!

! add your sqlite3 plugin2 here...
